{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["qiYozHtk8Dwgu+okKvHMjKgBydrtNGexdK6g1ENbay4=", "lWzHqFTn22Zdwh/Syi9v6rBlzR4GW6p2S9/RQptdyIs=", "C2X9j+dwhaYSGkvVgQ5v8HAXyjPlL1GL/aL+1J1Xjh4=", "E1/D46o9Bt9nRCEfJJiDqh8UjHvky/NXdQ7+kIq1ypw=", "81pOdzrhJ7Um6zXnIoYcIZksGGzjbqY6sV2KUJyPmKM=", "CMMw/3gwMYDiyWtDyIoAnc7rAoB7xlexuGlJ9/tUz/A=", "g1+LSnfgciHAdiZWE6dpZfv2depjfkdRk535mslYdL4=", "/SvRB2d43ZrgKUG7fUfRviSAdF3k/o3dcNip7UU/C0I=", "B0nfBJ25S3tz3KnSFM+efc1A85/6xIo7oTc5wSfj7Do=", "ydiL4oldu82dQTZD2B1itiP0803+YFscdcEChsP3sMk=", "K7jbDhApjLBESHUXk0CJkQGSUKr4Ivt7diTtQzGyvmE=", "4d07M+Ype9C2dtKuFmA89MCd/LdREyfWQpxXVqWokV8=", "g18Af6vu6FvPDF8VSMrbhupt3baNrfRVEVK9FSVJ6n4=", "t32rScTDvaNUa9ApIyvnweDpxN9NjRXY2+VDRE4Xd3A=", "3q7uLvjY1Ost70a+zauPByjqIZj8ED2JLw7ssZHj3do=", "r70CWD7XTtxb1q7oJWuT7XpWeWtVMtRk+P3wQqJpmro=", "Yk7lQUZP7nMjoSP+ZyUMwQ9A7x1CIhVFoc63zYbz48U=", "URKtAsfGLp2ppOQ5+nGt/rZn3NGpzjOhtFemFm3GItc=", "N+jn2cVoobH44u6BnljhIyY8wofhoZBTJSHjeYlF+sE=", "Xe9ar4jWKjpssBkUNs6idHffLnCvlb6bYuwFEZ85oAk=", "se+dN8bIfu9IOel/LH+YOqN34aLCvqIwYC+yoKY1Gyk=", "acA8OO6Igi/3IV7Th29nUFd/3snLG4ONPwVFPZmNXOo=", "zX2uO05otQT7LP0LtM13m51zXfdmtiS+rZ7z+xppzCI=", "44VD28pTLIwD0YAQaCtN2BEa/5D2njUaMx7irr1Zbsg=", "EfD60hIys8lslHmSsWhvKCpd/q8bPkUdqoN4PBegBE0=", "0eSVyO1O9go5Vfk5egHCbd8O5/Gn3R+7vBnacpMUjAk=", "aqThoqwDAA3H0JEsQt4StBoJRbiKeMzlObtu1YEVFE0=", "yn2WX9aLZTt8NeSRJ/qDGxRivJgk64MYSePGejmOt04=", "9oZbpeoGXt6G/2MhP4MTxEynkNApKc+2Z4K/VYstHPQ=", "IGuX997Zs2Htjrf6PqcsRNTRrlBacDt/c+4tAoUA+VM=", "Wv+cbhKqUOCZNAHj8qOSKGuHVpEzv941tq9fOkGF2gY=", "8OeU556gwNKsceY22ICbmScTfVhDhbLUKsemN/E/K6U=", "m5PX0EYA0apGJsB1VTAOitlySjVvRPT+suSqWsC/Mns=", "GOcDuqqSxwSzVXJMreHeKtx14WMCX3k+0C6KSrLsrSs=", "NJERGT8SrRVRxLAxg/kG4FQGoO3vypVVreGyCecxfpM=", "0p1TC0XZ/P0tDUHCOkZdjhFVIzpltJ/2l1eFlPqKNLY=", "2TaeD1yRP9qlFBdere2hGouVfUz4lNlcpw5r8j/udS8=", "ZpURzh0jYXZ2Zt/qeZ4KQWvPDWtx/zYZZXfPHuXV7hM=", "t2W6SUmBYcv6NCUEHZZ/XR2sSIZE4dKZD79sSGIvlsQ=", "AnujmUnHp3ldsCOl9xN36J4D+zIXyiHpPZgUeAcrXBE=", "SBzT/IkivQULBuMQ1+uSqqAtmwTQtBId4PvQUYs/PDA=", "03FIimBMTwSesoKvP3v5480NzjPLAvtsPnZY1CY0L1s=", "S5B9UpPKEdhYLKMluOKHbDKpXR0Jqf6iq7ANbtm9+vs=", "DkjiiQVryc6WFfMqX9fqtj2aYRvQJBOOBRBcEd0cxq4=", "m3Swu5PY0PIcstUZ2PYtiDILN/5jd0QOyuMQgnwaxM8=", "TYlF6K5PslfxEpYOPp2eVZyEYLcTzHtwhUZ5qXSQt9w=", "7Kaq519LeRGtLoRbiEW0GPfWQq0Qp+MwCvWO7tmJZis=", "MiRzMJDOinjfqjPb2Exbw2k0YTy84CroRXebGagQ5Rw=", "9IhUuepfFFZvEB6nejtkmTfb1O8B2bueQBPEeebhcUo=", "GP9cJks1VbczV4XGIg4lLjkX9Kh81Rn1anCB7iBhBuE=", "Ut7+hSL8LDBWkMQQJb2Cr6bVJFLJ1f7tGTN+BeqZRf0=", "bt7+OTB3FDHbjqTMqNo25utay0Q6RbTAidlRBf5wOKA=", "m82RfR1LLDDFYdo1Y4nOK1N9fXIziZt9xDX/vepFnNI=", "LIY6xqWPB/SYKYxo890jeJ5S74D4HtTvOqYHZiMn1aY=", "LEStMGQOcxWfyg3bpNB5SxEjY/SOONSfsXt+uJ6sKaE=", "7XUFSRXQvO7td0A31ft/96/Tpav+1qKAKLtZqUZqasY=", "Cr2RhurH10HSknZWKRvLAsb+katyIVpeZKOaKknq7jo=", "2K2cKoew5TBobDE/0II66aTqo6uZ7ZzqRL2P8nNBnQ4=", "eyowZCzIqu5RCM8oSFDHYhNgbX8qA10Kep3GijFSuf0=", "TfwILHMr7DSsEHDqR8Q58c9m8y1KBCZ6eBdE6/13q8Q=", "rgwBbhO0kGODm4wCP85wT8z1DfxPY3DOCmQ4V/b8XK4=", "aUoOtinSo+QACU6RnmOLRRdCUMRi9UHOuNQ8yP8nEYQ=", "aeFVf+l/zZDc6Z+paU82soewUymV2//88DBmX1li3QI=", "SzjP+vOeD4fLAIIbSahTnNRsAlPV4S0+v7CKTr1nGJA="], "CachedAssets": {"qiYozHtk8Dwgu+okKvHMjKgBydrtNGexdK6g1ENbay4=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/plgeurwc01-b9sayid5wm.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "css/site#[.{fingerprint=b9sayid5wm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/css/site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pi0kzaqg9p", "Integrity": "X7WdvJWe5+793Q+I1aPv6O/n2+XRWJsByQEd8dEKmGs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/css/site.css", "FileLength": 318, "LastWriteTime": "2025-09-07T14:06:34.3643463+00:00"}, "lWzHqFTn22Zdwh/Syi9v6rBlzR4GW6p2S9/RQptdyIs=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/hcke43rrfq-61n19gt1b8.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gjtj35e07v", "Integrity": "tOy5kS1PTkjkEg7bjV8bJa0VpoEZJUHnbqYmm1YyhMg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-09-07T14:06:34.357345+00:00"}, "C2X9j+dwhaYSGkvVgQ5v8HAXyjPlL1GL/aL+1J1Xjh4=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/f9vqtch99f-xtxxf3hu2r.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/js/site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wzaqhcazp9", "Integrity": "LHuc18mXYNZ0bRgJHGLPvUJogHOb9WPItN01M/KFqj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/js/site.js", "FileLength": 189, "LastWriteTime": "2025-09-07T14:06:34.4383678+00:00"}, "E1/D46o9Bt9nRCEfJJiDqh8UjHvky/NXdQ7+kIq1ypw=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/atiqvxx82x-bqjiyaj88i.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t6jyow2ejt", "Integrity": "pc3wV8tEXyJOebR7ZU/awGdi9J8M1YSpOQ0GfmB0jsI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-09-07T14:06:34.4373711+00:00"}, "81pOdzrhJ7Um6zXnIoYcIZksGGzjbqY6sV2KUJyPmKM=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/jt69d3n297-c2jlpeoesf.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qv53nyrc9m", "Integrity": "etADIvPQ++XM7GLq9OLsigatXdZlEKhhquv3EENwXYM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-09-07T14:06:34.5402791+00:00"}, "CMMw/3gwMYDiyWtDyIoAnc7rAoB7xlexuGlJ9/tUz/A=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/wiryyv2z2z-erw9l3u2r3.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nplkdnog75", "Integrity": "Fey2Qnt9L6qRCjDsSyHEc+hUYSLpk1VpOMVLtOWQkPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-09-07T14:06:34.6597183+00:00"}, "g1+LSnfgciHAdiZWE6dpZfv2depjfkdRk535mslYdL4=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/p7i2eirjm6-aexeepp0ev.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x19euzbaop", "Integrity": "VZ//8tsmm/peht1yvc7BlCC2l9jykWxgolFNNBRq3iA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-09-07T14:06:34.5105087+00:00"}, "/SvRB2d43ZrgKUG7fUfRviSAdF3k/o3dcNip7UU/C0I=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/p6wbg186eq-d7shbmvgxk.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "de3myh4ugx", "Integrity": "Sz/4vyRFDomC/KgO1iIBNyVxkaoI5KEWCsMoGbjpAbo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-09-07T14:06:34.6316802+00:00"}, "B0nfBJ25S3tz3KnSFM+efc1A85/6xIo7oTc5wSfj7Do=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/eqp54b8lr2-ausgxo2sd3.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "llvdsfuo7y", "Integrity": "xwbgJufxIF+fKh7W7rJOriFSpnA6Z1e0dGLZ8xZoFf4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-09-07T14:06:34.3992486+00:00"}, "ydiL4oldu82dQTZD2B1itiP0803+YFscdcEChsP3sMk=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/tekqbeg4ih-k8d9w2qqmf.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8basbbqy9z", "Integrity": "NDo0uUYPt107zSN2+GQq0MWUIdA4tbBWTy3B2T5mt0g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-09-07T14:06:34.4002428+00:00"}, "K7jbDhApjLBESHUXk0CJkQGSUKr4Ivt7diTtQzGyvmE=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/msqrmfzi2e-cosvhxvwiu.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hwotdmx7ke", "Integrity": "r8dihBWtRuflA1SshImDNVwNxupE3I4+paoNH5v5y2g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-09-07T14:06:34.4205115+00:00"}, "4d07M+Ype9C2dtKuFmA89MCd/LdREyfWQpxXVqWokV8=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/as3oy0t459-ub07r2b239.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xmt4dzeh8b", "Integrity": "99fh+Ouc0FukemvqpWJthoZTMmr1ZfsWXS8Eko1mo9U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-09-07T14:06:34.3922098+00:00"}, "g18Af6vu6FvPDF8VSMrbhupt3baNrfRVEVK9FSVJ6n4=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/aki68h6fc0-fvhpjtyr6v.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q98uu36txx", "Integrity": "Xu/ywItCfSxVbbxuEI0ITLuPgYoQIGDVe13YkeJ/nuw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-09-07T14:06:34.536757+00:00"}, "t32rScTDvaNUa9ApIyvnweDpxN9NjRXY2+VDRE4Xd3A=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/l60lrw0jk5-b7pk76d08c.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xjqcbp9bzb", "Integrity": "bVBAValmreEE8qqeSbiezAvxjVdi8uEUBlZ8VQkpdxY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-09-07T14:06:34.6713697+00:00"}, "3q7uLvjY1Ost70a+zauPByjqIZj8ED2JLw7ssZHj3do=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/yt0a7p7v4l-fsbi9cje9m.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qs39geit3q", "Integrity": "euP8e7V1Zn9c5ax4QOLwaTIFdDnD1FwYWI3wM9m58To=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-09-07T14:06:34.5669698+00:00"}, "r70CWD7XTtxb1q7oJWuT7XpWeWtVMtRk+P3wQqJpmro=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/n3edm74k0u-rzd6atqjts.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yiofxmsa8v", "Integrity": "Fr0UmnGwfb79O1UiS2iBYDv5MP+VyalRS+prbPLMzus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-09-07T14:06:34.6487191+00:00"}, "Yk7lQUZP7nMjoSP+ZyUMwQ9A7x1CIhVFoc63zYbz48U=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/elf0fbbvwd-ee0r1s7dh0.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0yn3oohjbt", "Integrity": "CBx5dddLjL6jyZIWwZ8xwYxsoJUQfgtgVh+b5XgAUJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-09-07T14:06:34.3873489+00:00"}, "URKtAsfGLp2ppOQ5+nGt/rZn3NGpzjOhtFemFm3GItc=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/1lckc8904z-dxx9fxp4il.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "83ibl6bdqg", "Integrity": "VC2bV11abiRbZU+opSenUTXUAibJ8wP+8eKJvad/6m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-09-07T14:06:34.3613463+00:00"}, "N+jn2cVoobH44u6BnljhIyY8wofhoZBTJSHjeYlF+sE=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/lf8j8j6t8p-jd9uben2k1.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xzyp91gvov", "Integrity": "k3WNqhVR7f6rfrJtq9VFbqv+m7u1fGufHTgjssmCQIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-09-07T14:06:34.4069929+00:00"}, "Xe9ar4jWKjpssBkUNs6idHffLnCvlb6bYuwFEZ85oAk=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/tgk972j05l-khv3u5hwcm.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lb2ax2er70", "Integrity": "vyx7RcdwvnTfx70lnbZkyl9ZtPX6H0Wzw0U66Wg/Ih0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-09-07T14:06:34.3753512+00:00"}, "se+dN8bIfu9IOel/LH+YOqN34aLCvqIwYC+yoKY1Gyk=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/9exndt4m2s-r4e9w2rdcm.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zpcvsmmi2i", "Integrity": "J+aDgW/XAfgjCVwwYP82sXB0u8H3CBj5baCGeyPVq/w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-09-07T14:06:34.5175099+00:00"}, "acA8OO6Igi/3IV7Th29nUFd/3snLG4ONPwVFPZmNXOo=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/dworxm3kqg-lcd1t2u6c8.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bra8xlaz3i", "Integrity": "iYKYqA8UQ1ihqJMeu/hJ+eD7QXjXkTCIlDpMYpLPj68=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-09-07T14:06:34.6306791+00:00"}, "zX2uO05otQT7LP0LtM13m51zXfdmtiS+rZ7z+xppzCI=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/rmsmp3ev4v-c2oey78nd0.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a4emzht0ak", "Integrity": "+qfEf74fYZcxGlJxLRXw29QR/dKmIyxbYFB8o0niDIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-09-07T14:06:34.5205066+00:00"}, "44VD28pTLIwD0YAQaCtN2BEa/5D2njUaMx7irr1Zbsg=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/ouocgudph5-tdbxkamptv.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ticab1qw3x", "Integrity": "G3NNvGGd9NifdVm4J+4bJhb/NfQMnJdBuYCr9yTMF74=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-09-07T14:06:34.6166809+00:00"}, "EfD60hIys8lslHmSsWhvKCpd/q8bPkUdqoN4PBegBE0=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/jm17pwaskg-j5mq2jizvt.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y05hho4joi", "Integrity": "TrtVB/NKd5KHvPHeEXAr18Yfbhc4otb6oAcl2TxPv2k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-09-07T14:06:34.4458845+00:00"}, "0eSVyO1O9go5Vfk5egHCbd8O5/Gn3R+7vBnacpMUjAk=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/z6ylnxt33e-06098lyss8.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ivlimefe9h", "Integrity": "Pn08maprrhw5DTrqh4newsQpePUCfx9fxijw/Eq0FeU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-09-07T14:06:34.4175159+00:00"}, "aqThoqwDAA3H0JEsQt4StBoJRbiKeMzlObtu1YEVFE0=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/wzez5j1x5t-nvvlpmu67g.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yxaxjpj2zo", "Integrity": "plx2oQEeR60jH0/b817B21lxJBcijHB9tLUu8ZWE0IM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-09-07T14:06:34.4657535+00:00"}, "yn2WX9aLZTt8NeSRJ/qDGxRivJgk64MYSePGejmOt04=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/bbak4e5ife-s35ty4nyc5.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkyby1hkov", "Integrity": "zZkLTFGBa+N46XqOQpLIuz3qQ8TVabui1jCD1zzhqyg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-09-07T14:06:34.3783458+00:00"}, "9oZbpeoGXt6G/2MhP4MTxEynkNApKc+2Z4K/VYstHPQ=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/mafedc6m7k-pj5nd1wqec.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ieclp98g0", "Integrity": "bZ0mhRTesxtxdVxduHjChjIuMo+bNzO6g++31seutGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-09-07T14:06:34.6381968+00:00"}, "IGuX997Zs2Htjrf6PqcsRNTRrlBacDt/c+4tAoUA+VM=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/s688x79q6z-46ein0sx1k.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ta814kukfc", "Integrity": "RRS8nI5OD8lm+2LTe3CimMlA3c6b5+JKzJ4B6FpGYuQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-09-07T14:06:34.7086029+00:00"}, "Wv+cbhKqUOCZNAHj8qOSKGuHVpEzv941tq9fOkGF2gY=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/kwkfx39rnf-v0zj4ognzu.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y6rpgaobwt", "Integrity": "FvJuSMP2YMvmC+BvG+l+4oKlt+d41a9tXVE5TaIaicc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-09-07T14:06:34.536757+00:00"}, "8OeU556gwNKsceY22ICbmScTfVhDhbLUKsemN/E/K6U=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/0so9lvhu00-37tfw0ft22.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9cwgz03a4k", "Integrity": "ubq8orZ6fCxhzuD2xAJWPh7of4RUz1ZC+LZBWgNXDCM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-09-07T14:06:34.6985637+00:00"}, "m5PX0EYA0apGJsB1VTAOitlySjVvRPT+suSqWsC/Mns=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/q603qz3b4p-hrwsygsryq.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zsi2r52cm2", "Integrity": "sxrCEPizO/oGb+PjbNzNkSY01EmjjnTghVkyX4PcaU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-09-07T14:06:34.4175159+00:00"}, "GOcDuqqSxwSzVXJMreHeKtx14WMCX3k+0C6KSrLsrSs=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/zjpzmircc6-pk9g2wxc8p.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "utzeln9p1v", "Integrity": "HpKduG0LPCnTB73WyDjV1XJiA2n55vxAdfz5+N+Wlns=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-09-07T14:06:34.4528865+00:00"}, "NJERGT8SrRVRxLAxg/kG4FQGoO3vypVVreGyCecxfpM=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/lkfe2vxgb6-ft3s53vfgj.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mapa9s0nlq", "Integrity": "jveMlVd5N9Rv8Y2xeGiEKZDcfCnnAYIyis0noH4HRbM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-09-07T14:06:34.3882117+00:00"}, "0p1TC0XZ/P0tDUHCOkZdjhFVIzpltJ/2l1eFlPqKNLY=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/4en3v8dh7n-6cfz1n2cew.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iks902n20r", "Integrity": "4jD/MJ8V1KpkqYUhwGHEP96bA1HG/EKzzdID2Y/XFaY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-09-07T14:06:34.4997479+00:00"}, "2TaeD1yRP9qlFBdere2hGouVfUz4lNlcpw5r8j/udS8=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/2ht3mbycqk-6pdc2jztkx.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f47el4ako4", "Integrity": "j4C6/l5/VktBAGO2tzhvkg2On432spHGetOb0zM/lng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-09-07T14:06:34.5815203+00:00"}, "ZpURzh0jYXZ2Zt/qeZ4KQWvPDWtx/zYZZXfPHuXV7hM=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/ss4tkdj3c0-493y06b0oq.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d1pecmyxtf", "Integrity": "QXrX67dKCMdhIT6OvT0zgftz+wu2XSxjyBlMsmIENQQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-09-07T14:06:34.6965639+00:00"}, "t2W6SUmBYcv6NCUEHZZ/XR2sSIZE4dKZD79sSGIvlsQ=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/wx2frofdgn-iovd86k7lj.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rzoepyx18s", "Integrity": "+bjzmfX5lPuCupxKWq/ohX9O3Fq7iwK8faGFiD3QssA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-09-07T14:06:34.6206813+00:00"}, "AnujmUnHp3ldsCOl9xN36J4D+zIXyiHpPZgUeAcrXBE=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/0k6pkx7lef-vr1egmr9el.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lqx0uptmf7", "Integrity": "MyNLSRohJhqvR3grR9ZgvgrxU2FqeUMfO4gA3BNa/Tw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-09-07T14:06:34.702603+00:00"}, "SBzT/IkivQULBuMQ1+uSqqAtmwTQtBId4PvQUYs/PDA=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/qjyl94vguq-kbrnm935zg.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kfnqxfmd53", "Integrity": "IPal6iEIeXY+oO++FL+ujAbaZz2DQejhgt8x9Fw/Lyc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-09-07T14:06:34.4285111+00:00"}, "03FIimBMTwSesoKvP3v5480NzjPLAvtsPnZY1CY0L1s=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/x6mkgld0db-jj8uyg4cgr.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xl0jhl71e6", "Integrity": "1V6+yFJIywtUmypyWHCj13e/hMbrvGjWF7AtHTj7y88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-09-07T14:06:34.4578869+00:00"}, "S5B9UpPKEdhYLKMluOKHbDKpXR0Jqf6iq7ANbtm9+vs=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/pzwa7d5815-y7v9cxd14o.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hf8l0l1abk", "Integrity": "E94YLFAwUcOKC0fKHFJ+2k6fv156l8Ftxru1cbrNzvA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-09-07T14:06:34.4588831+00:00"}, "DkjiiQVryc6WFfMqX9fqtj2aYRvQJBOOBRBcEd0cxq4=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/gjniralk8i-notf2xhcfb.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ug42gx2397", "Integrity": "4mjnv8wEsIPqFZ44yOygGYlGftJdqqFxCTXXzEYGCTs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-09-07T14:06:34.5499666+00:00"}, "m3Swu5PY0PIcstUZ2PYtiDILN/5jd0QOyuMQgnwaxM8=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/whrh3jxfo3-h1s4sie4z3.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w5smaoxcji", "Integrity": "FPIWN2C69yIYQwtPYEzfaF2VJOQ8E/FmHREomNVoHHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-09-07T14:06:34.702603+00:00"}, "TYlF6K5PslfxEpYOPp2eVZyEYLcTzHtwhUZ5qXSQt9w=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/xcpntce7vg-63fj8s7r0e.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7355urbamp", "Integrity": "/fzN5m4HpCinhQgyhv9a2GoLOChbhOzS2FxhpXWIfeE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-09-07T14:06:34.551972+00:00"}, "7Kaq519LeRGtLoRbiEW0GPfWQq0Qp+MwCvWO7tmJZis=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/02vjf4n6ru-0j3bgjxly4.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dvmyza30ke", "Integrity": "SUM9WWxVgf0VNNBf9Zf7iga7Z4tkGvbKAz0ev6eeJO0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-09-07T14:06:34.670366+00:00"}, "MiRzMJDOinjfqjPb2Exbw2k0YTy84CroRXebGagQ5Rw=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/aezf3cup91-47otxtyo56.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "twtrqilhqb", "Integrity": "LUJC/c/lAEUqUN5H7xI/zyKVpIQFL/ghfMpMmc8OywY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-09-07T14:06:34.6056438+00:00"}, "9IhUuepfFFZvEB6nejtkmTfb1O8B2bueQBPEeebhcUo=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/sj0o4f8544-4v8eqarkd7.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kvtlytiqyp", "Integrity": "gCDjtbmTQ7oHsIcHiLvIK84vX7beVyMV9KBKNX7RvYA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-09-07T14:06:34.4578869+00:00"}, "GP9cJks1VbczV4XGIg4lLjkX9Kh81Rn1anCB7iBhBuE=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/e6wheytmsa-356vix0kms.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fo9enwqfr", "Integrity": "HxJunWv7ekzhB184/5lStP91qJcW7XRDqOATbPYdAB0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-09-07T14:06:34.3663453+00:00"}, "Ut7+hSL8LDBWkMQQJb2Cr6bVJFLJ1f7tGTN+BeqZRf0=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/ylttrv3j1o-83jwlth58m.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4oqpnpgpyd", "Integrity": "RA6FnFwvZwcy7PIS40oCJIxSKEHPVQl0ukyGVULfdIM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-09-07T14:06:34.4957446+00:00"}, "bt7+OTB3FDHbjqTMqNo25utay0Q6RbTAidlRBf5wOKA=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/daradmyshj-mrlpezrjn3.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8vc9x<PERSON><PERSON><PERSON>", "Integrity": "nAkd+bXDCLqrA9jmHlM5YCYXVOPFw+/pJ2IBWBBluZc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-09-07T14:06:34.576551+00:00"}, "m82RfR1LLDDFYdo1Y4nOK1N9fXIziZt9xDX/vepFnNI=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/9lpbv05n85-lzl9nlhx6b.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b61onlgcie", "Integrity": "8wakphbzjcA3gIBAEAcZh4CWWnS9t3pv10WlHgDBKAQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-09-07T14:06:34.7076032+00:00"}, "LIY6xqWPB/SYKYxo890jeJ5S74D4HtTvOqYHZiMn1aY=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/andlqkz2yp-ag7o75518u.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aq3nh51dc0", "Integrity": "XlgSExzxNa3nkGlIRQzYeUGryaFcdCYYmDjJzMcVoSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-09-07T14:06:34.5225096+00:00"}, "LEStMGQOcxWfyg3bpNB5SxEjY/SOONSfsXt+uJ6sKaE=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/o5s4nqgah0-x0q3zqp4vz.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s3lpkwkcby", "Integrity": "6HStD59bjkTPjQ3fGm7jnZcqoab/ANf+vA0DdOBKEcQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-09-07T14:06:34.6657207+00:00"}, "7XUFSRXQvO7td0A31ft/96/Tpav+1qKAKLtZqUZqasY=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/5wyabwbwqr-0i3buxo5is.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0cxuflfi36", "Integrity": "wKh8lMNTtLKwUKNMLP8uDqnSQs/rLxp7Le9dXpyys9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.js", "FileLength": 84431, "LastWriteTime": "2025-09-07T14:06:34.6246812+00:00"}, "Cr2RhurH10HSknZWKRvLAsb+katyIVpeZKOaKknq7jo=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/zclkhg7zcn-o1o13a6vjx.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pjwk5xzizl", "Integrity": "OyU7TDIA0tTFKi3MRwhEaOd0kz4cUNzCtNouAea7Wq0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-09-07T14:06:34.4687463+00:00"}, "2K2cKoew5TBobDE/0II66aTqo6uZ7ZzqRL2P8nNBnQ4=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/sfdtm3yt31-ttgo8qnofa.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vqao1ymr7h", "Integrity": "Z9Xr9hTrQYEAWUwvg7CyNKwm+JkmM5dZcep0R6u6EHU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-09-07T14:06:34.4967458+00:00"}, "eyowZCzIqu5RCM8oSFDHYhNgbX8qA10Kep3GijFSuf0=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/49ru4jfgx1-2z0ns9nrw6.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yeuxext30b", "Integrity": "Opr5hmrLHeEoBUrMugSdt/Vxh7nPIrdbPnxEe2XaKoA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-09-07T14:06:34.4957446+00:00"}, "TfwILHMr7DSsEHDqR8Q58c9m8y1KBCZ6eBdE6/13q8Q=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/prk5ors1wn-muycvpuwrr.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tv1px50b4e", "Integrity": "N3WCKGekGDic3HiyYi1ZwSMVPxgAzjd22gI/cZzwGGs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-09-07T14:06:34.5532314+00:00"}, "rgwBbhO0kGODm4wCP85wT8z1DfxPY3DOCmQ4V/b8XK4=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/g9ngfeipjr-87fc7y1x7t.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xqbkylpnx5", "Integrity": "eZ5ql6+ipm5O69S+f/4DgMADzzYHAfKSgSmm36kNWC4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-09-07T14:06:34.6617198+00:00"}, "aUoOtinSo+QACU6RnmOLRRdCUMRi9UHOuNQ8yP8nEYQ=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/4lkxi7qll4-mlv21k5csn.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "my2pbmxgqc", "Integrity": "Pu7EEldYFfJduO8Z+fI/nS9U6SNQun+UzT1IrRHJ4oA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-09-07T14:06:34.5245073+00:00"}, "aeFVf+l/zZDc6Z+paU82soewUymV2//88DBmX1li3QI=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/0ic3yl05sj-1b6d9wnjpo.gz", "SourceId": "SampleApp", "SourceType": "Computed", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "SampleApp#[.{fingerprint=1b6d9wnjpo}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/obj/Debug/net9.0/scopedcss/bundle/SampleApp.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dpcmenmtd1", "Integrity": "0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/obj/Debug/net9.0/scopedcss/bundle/SampleApp.styles.css", "FileLength": 535, "LastWriteTime": "2025-09-07T14:06:34.6733614+00:00"}, "SzjP+vOeD4fLAIIbSahTnNRsAlPV4S0+v7CKTr1nGJA=": {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/p5g9so0ja5-1b6d9wnjpo.gz", "SourceId": "SampleApp", "SourceType": "Computed", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "SampleApp#[.{fingerprint=1b6d9wnjpo}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/obj/Debug/net9.0/scopedcss/projectbundle/SampleApp.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dpcmenmtd1", "Integrity": "0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/obj/Debug/net9.0/scopedcss/projectbundle/SampleApp.bundle.scp.css", "FileLength": 535, "LastWriteTime": "2025-09-07T14:06:34.5805213+00:00"}}, "CachedCopyCandidates": {}}