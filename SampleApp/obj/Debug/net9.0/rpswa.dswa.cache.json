{"GlobalPropertiesHash": "mpt4DrCit+t4yTVWmtSCDerEczP55UKoajEonTMptqU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["47dRo5ohAq3LygOySSwlQUPVDbROKNI25gSbJmbt+Q0=", "R50bgeM33wEGzPnf1IiZwDNqW15jag9wp6FYUapvUnY=", "f7FsXL/wl9N8xHtGhsX0jIePg9dW6beXkeochtMlG0o=", "ZjK7fMasws+vbnBH1cThzdfyFT5L6tmcUr+57TKXN40=", "psK5kBL+xpIXDOjuqicKTzmd6EwDImSOF5JgZ4sJHmA=", "uFsPwUlozxuFyGUy2KUKKy4BZdAnSbaBggtKG16e/TY=", "L/1N5y1ZnreDZ+oUmaWvCPMbsaUuLtX0Khd5tt2pXr8=", "eFVqFu2ILqbX8TSwqyH8oHbi/+hCioV00Ka+poIjIa8=", "vnXVOwm2w33tNEUBFCl8FIlyWnYEoSoc1Y+sjOxOSt0=", "udGXBXvTrziHiSbMc7HpbazXBTWkAIGq2CWKqH0qfsA=", "h3g0yTCrr6oYDLQuoU/ojoD9X2eBoHkMu2ZRHlBxf9k=", "RbDJryJDYWAQ+GzNdiYex//oZeMLAj07yAIGfhnyaTE=", "yG72SMxEw+9MD7I6u5DfMqLUg+2Yt2Hv8EZIT7+xUtU=", "FHgcxKQybS9Eo47cjD+TXvmw6+xTNb90O5WZ6IOLwEY=", "VMUM+s2tqFJ3fGaHBZpGRe+FXlh1i8qH0vivHyRapPE=", "62wSK7muBJLUzNAEFJkXw+QDeDqCwMMKv8QavQzn1gU=", "PdIRc2xkferip+hdWMonwRNiG9eID5ZPxf0JR4bUKfs=", "vDAtMYEiKK6CLIDTdGGYidJySAB0Pfa9Mou66Q0iXmM=", "GX+xSmFBM8gR5RaBMrG5n6YY9Hx/MY6JLOs7jHNguzs=", "pJ9EDVbozFcw9YM12zFEbL/Ib/DiovSIq1eEkBY5/1Y=", "eAOo5XkzHolHUZv7gQnqvOI31NKIc8gIaJTQxCvI/XM=", "BAOmmzPO3gj5r9W+Cu46A9BgHUFvoEWkgqiTNmt6Emc=", "cUoZRzOGSMsjP1FS+fBjRHuJDQKU14B2TdvgA3pIPUI=", "dE1EFHdSBiJL814kXQAg0tml2N8jSUJuGdceRhLpGsA=", "zJGJrR+kEW71Vd6XP3JnOiBdyAUPnbfY98z6CzAJdDQ=", "tQdTXnVOmbvfzViDZetd/PEj0H53X9JIA+fE4UUUB5I=", "BPC+wYw7X0oNldeWdsdzkO1lECVJuadak6dK50469Wg=", "tgjkuNRCf+0HzoS57yYW0RgjnRbSmD4gi+nqzYbH05w=", "sN8OcH28JQBUhtdBi6FfAh1xx74QgJNUNCwKtZi405U=", "KxO7XvnMoU411sz6pASj2CXn/Fl0dLApZamfe3qy6J0=", "p1arRuXEvCKOUsQGT6KV9Ab+Y0HNuILdkMW6dYKUJMI=", "mxUXeDbNFlRfzp+Z52S5r6gf3/Z5F8kLT3zPBM5pPLM=", "9DfrjaXlWQ1j8ZzscB76HBeZ9NplRW14PvZWejlefsY=", "pbKNylIX0//GyF5lOPjGALgogDdS8iQxNcYk4a6tOCg=", "Y8dcGP2MtbaXFtroM2kQM6e13INKNyM/ziCBPQ9AITY=", "TgNllmk9X/nF8ldwjvbotl1LApufqkPIf0+yTkjQivQ=", "eJ9cfmDT2eZ0eZO4/ZwpAUkCxJDlr5iSICf6SaLDEj0=", "l7ZS77EYTwGEGLLc6UQ0Kz533JF62A5jw8oW8w+O5C8=", "HTHgJmJctIFgqOyiBJyU+TwHHT5Wlatc8pq6S7WTnpw=", "HnsgM3pIXCTegni7X8Di61QjKI+9l/6RaTNgLyZ2hS8=", "JIbD0XPLEcxtvMZUWpmBWAeA1vpKEi1un+8BJR4JpzU=", "Re4ttnZxAtVtxuzVdHNF9MKdfkmyEmb0dVT2Gzgw4OI=", "cX3DabCTZQ0RukYlQAo++++TR02MrQIJlKJI4yR+XTU=", "H1AEUAku1ovxPRwETNncMxXj6vvsbSsikus+SoAdg3k=", "AOAnSiQT6lzdBYQrqHoqUHeI9RLVRCb6aVZat+lqAv4=", "uXX6wfIwhR5DkTbp3chktffZJS6yj+2OoSYjhFgQlOo=", "0ewlml8C/wAo2MzuzobBltsiUjKZ26vMzX3IxlWexH8=", "ahxbnpX41OC3pBkIse9NfJOi5N9GLJ7Sw5qLePAM0Mw=", "FuyWjTRdkJuNkIZAgpMBhUA6D1m5/1MYB10QSctzBVM=", "SEAQl+EjMmWz3Vj7fyP9Rnt1k2fFNN0d1OZpFpc1/QU=", "Mnx0tDJqCFpXtJxnTo7OX0JX/drio77rwiHGMJzt/4o=", "4BD1BR0lJnLEl2+iU7Nbu6rFVeypkbeM+Zx/U07vKtI=", "04xCbfeiK361SvdqZ93vWsbRSCE2LAeNqhOIOnz4/AY=", "RPPkIMs4VriAJuSdn/mRB2Ld/fBxnUDkQvnI0BmhMh8=", "bwpSwNYMxIAlpKfyxjCpV74LXr46ej2wfYAdL0L/HGg=", "Odo5xklBcaO0IvvbQGQ/BERw4TomeEiwRaNE8S3n0Sg=", "xG5YtYunQp1PgsF0GiF7P35KtjI69Xx4ZM4ed/sI5TU=", "fe8I5zfeZ37b4e+xU1IF8JHXtb3H6n5U0FSSw0S3C78=", "aRIho7CJGPtDQbep6gBYmcdje5MrFLROHC+auyMZY1c=", "ac3caHPPokd+2+CnWlZVwglM9tJOy063Vl/K9dwmrtY=", "pfqcMLB6XlHvPFBywS7qKjbhEbDhog7jjuGWQxzzX+A=", "V6q4Yg1Jim9/1qEOU2yCcvXD+WeDBW9DL+EMQZSX2wc=", "qIdQ9cmSVKr9eDFurFnTOeKY7S4PcmnxKY5lWgFUNd0=", "ENQA0Hgo+mC0LLO3+NhysA350ZpAca9/B+1I4dnjSdk=", "ph4i4MrHFHyYPi+dwS4WLQlsU9VJ4upySlpAq9nWv3g=", "XoVGXBJ80P8Ez9KpXkHIDdctb1gEbddhTwEqlFjhrrU=", "myP8lpljZhBAbBJfK8cr2CaEI9d6CDz8GJkfAA4lGGo=", "Kx7CXNoAEyc+a9uQCzRVQAmy56RQi11nMBHWEsdGBDg=", "VgHL7D4SKPEy+VJhmlaMsl1yfEAgbVQ75spGC50MIjY=", "FMceK9tFrFjYaAECfhIe6GCtwdg8dBrxCdH518Phb+U=", "LReB3xoCDOMIjSdh2gTOyJcBSOMMowlNbpUAuXgNeC4=", "4wzhxbZn3KCmOwBHlkYA6wnuzQD0OoATh3w/mHIduuI="], "CachedAssets": {"47dRo5ohAq3LygOySSwlQUPVDbROKNI25gSbJmbt+Q0=": {"Identity": "/App/SampleApp/wwwroot/css/site.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/site.css", "FileLength": 667, "LastWriteTime": "2025-09-07T14:05:38.2731305+00:00"}, "R50bgeM33wEGzPnf1IiZwDNqW15jag9wp6FYUapvUnY=": {"Identity": "/App/SampleApp/wwwroot/favicon.ico", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-09-07T14:05:38.1331006+00:00"}, "f7FsXL/wl9N8xHtGhsX0jIePg9dW6beXkeochtMlG0o=": {"Identity": "/App/SampleApp/wwwroot/js/site.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/site.js", "FileLength": 231, "LastWriteTime": "2025-09-07T14:05:38.2806444+00:00"}, "ZjK7fMasws+vbnBH1cThzdfyFT5L6tmcUr+57TKXN40=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-09-07T14:05:37.1184426+00:00"}, "psK5kBL+xpIXDOjuqicKTzmd6EwDImSOF5JgZ4sJHmA=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-09-07T14:05:37.143063+00:00"}, "uFsPwUlozxuFyGUy2KUKKy4BZdAnSbaBggtKG16e/TY=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-09-07T14:05:37.1642139+00:00"}, "L/1N5y1ZnreDZ+oUmaWvCPMbsaUuLtX0Khd5tt2pXr8=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-09-07T14:05:37.1824239+00:00"}, "eFVqFu2ILqbX8TSwqyH8oHbi/+hCioV00Ka+poIjIa8=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-09-07T14:05:37.1985181+00:00"}, "vnXVOwm2w33tNEUBFCl8FIlyWnYEoSoc1Y+sjOxOSt0=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-09-07T14:05:37.2202336+00:00"}, "udGXBXvTrziHiSbMc7HpbazXBTWkAIGq2CWKqH0qfsA=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-09-07T14:05:37.2351925+00:00"}, "h3g0yTCrr6oYDLQuoU/ojoD9X2eBoHkMu2ZRHlBxf9k=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-09-07T14:05:37.2496159+00:00"}, "RbDJryJDYWAQ+GzNdiYex//oZeMLAj07yAIGfhnyaTE=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-09-07T14:05:37.2626156+00:00"}, "yG72SMxEw+9MD7I6u5DfMqLUg+2Yt2Hv8EZIT7+xUtU=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-09-07T14:05:37.2791342+00:00"}, "FHgcxKQybS9Eo47cjD+TXvmw6+xTNb90O5WZ6IOLwEY=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-09-07T14:05:37.2931342+00:00"}, "VMUM+s2tqFJ3fGaHBZpGRe+FXlh1i8qH0vivHyRapPE=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-09-07T14:05:37.3067797+00:00"}, "62wSK7muBJLUzNAEFJkXw+QDeDqCwMMKv8QavQzn1gU=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-09-07T14:05:37.321713+00:00"}, "PdIRc2xkferip+hdWMonwRNiG9eID5ZPxf0JR4bUKfs=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-09-07T14:05:37.3368938+00:00"}, "vDAtMYEiKK6CLIDTdGGYidJySAB0Pfa9Mou66Q0iXmM=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-09-07T14:05:37.3506067+00:00"}, "GX+xSmFBM8gR5RaBMrG5n6YY9Hx/MY6JLOs7jHNguzs=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-09-07T14:05:37.3658136+00:00"}, "pJ9EDVbozFcw9YM12zFEbL/Ib/DiovSIq1eEkBY5/1Y=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-09-07T14:05:37.3799582+00:00"}, "eAOo5XkzHolHUZv7gQnqvOI31NKIc8gIaJTQxCvI/XM=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-09-07T14:05:37.3969587+00:00"}, "BAOmmzPO3gj5r9W+Cu46A9BgHUFvoEWkgqiTNmt6Emc=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-09-07T14:05:37.4124765+00:00"}, "cUoZRzOGSMsjP1FS+fBjRHuJDQKU14B2TdvgA3pIPUI=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-09-07T14:05:37.4289861+00:00"}, "dE1EFHdSBiJL814kXQAg0tml2N8jSUJuGdceRhLpGsA=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-09-07T14:05:37.44163+00:00"}, "zJGJrR+kEW71Vd6XP3JnOiBdyAUPnbfY98z6CzAJdDQ=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-09-07T14:05:37.4588402+00:00"}, "tQdTXnVOmbvfzViDZetd/PEj0H53X9JIA+fE4UUUB5I=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-09-07T14:05:37.4738403+00:00"}, "BPC+wYw7X0oNldeWdsdzkO1lECVJuadak6dK50469Wg=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-09-07T14:05:37.4910085+00:00"}, "tgjkuNRCf+0HzoS57yYW0RgjnRbSmD4gi+nqzYbH05w=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-09-07T14:05:37.5056033+00:00"}, "sN8OcH28JQBUhtdBi6FfAh1xx74QgJNUNCwKtZi405U=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-09-07T14:05:37.5335969+00:00"}, "KxO7XvnMoU411sz6pASj2CXn/Fl0dLApZamfe3qy6J0=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-09-07T14:05:37.5502258+00:00"}, "p1arRuXEvCKOUsQGT6KV9Ab+Y0HNuILdkMW6dYKUJMI=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-09-07T14:05:37.5714053+00:00"}, "mxUXeDbNFlRfzp+Z52S5r6gf3/Z5F8kLT3zPBM5pPLM=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-09-07T14:05:37.5849162+00:00"}, "9DfrjaXlWQ1j8ZzscB76HBeZ9NplRW14PvZWejlefsY=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-09-07T14:05:37.606572+00:00"}, "pbKNylIX0//GyF5lOPjGALgogDdS8iQxNcYk4a6tOCg=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-09-07T14:05:37.6222758+00:00"}, "Y8dcGP2MtbaXFtroM2kQM6e13INKNyM/ziCBPQ9AITY=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-09-07T14:05:37.6448796+00:00"}, "TgNllmk9X/nF8ldwjvbotl1LApufqkPIf0+yTkjQivQ=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-09-07T14:05:37.657522+00:00"}, "eJ9cfmDT2eZ0eZO4/ZwpAUkCxJDlr5iSICf6SaLDEj0=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-09-07T14:05:37.6771899+00:00"}, "l7ZS77EYTwGEGLLc6UQ0Kz533JF62A5jw8oW8w+O5C8=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-09-07T14:05:37.7027658+00:00"}, "HTHgJmJctIFgqOyiBJyU+TwHHT5Wlatc8pq6S7WTnpw=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-09-07T14:05:37.7293609+00:00"}, "HnsgM3pIXCTegni7X8Di61QjKI+9l/6RaTNgLyZ2hS8=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-09-07T14:05:37.7579678+00:00"}, "JIbD0XPLEcxtvMZUWpmBWAeA1vpKEi1un+8BJR4JpzU=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-09-07T14:05:37.7908074+00:00"}, "Re4ttnZxAtVtxuzVdHNF9MKdfkmyEmb0dVT2Gzgw4OI=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-09-07T14:05:37.8097778+00:00"}, "cX3DabCTZQ0RukYlQAo++++TR02MrQIJlKJI4yR+XTU=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-09-07T14:05:37.8282334+00:00"}, "H1AEUAku1ovxPRwETNncMxXj6vvsbSsikus+SoAdg3k=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-09-07T14:05:37.8433925+00:00"}, "AOAnSiQT6lzdBYQrqHoqUHeI9RLVRCb6aVZat+lqAv4=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-09-07T14:05:37.860437+00:00"}, "uXX6wfIwhR5DkTbp3chktffZJS6yj+2OoSYjhFgQlOo=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-09-07T14:05:37.8750326+00:00"}, "0ewlml8C/wAo2MzuzobBltsiUjKZ26vMzX3IxlWexH8=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-09-07T14:05:37.8916733+00:00"}, "ahxbnpX41OC3pBkIse9NfJOi5N9GLJ7Sw5qLePAM0Mw=": {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/LICENSE", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/LICENSE", "FileLength": 1153, "LastWriteTime": "2025-09-07T14:05:37.6889742+00:00"}, "FuyWjTRdkJuNkIZAgpMBhUA6D1m5/1MYB10QSctzBVM=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-09-07T14:05:38.1967639+00:00"}, "SEAQl+EjMmWz3Vj7fyP9Rnt1k2fFNN0d1OZpFpc1/QU=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-09-07T14:05:38.2187557+00:00"}, "Mnx0tDJqCFpXtJxnTo7OX0JX/drio77rwiHGMJzt/4o=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-09-07T14:05:37.7690581+00:00"}, "4BD1BR0lJnLEl2+iU7Nbu6rFVeypkbeM+Zx/U07vKtI=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-09-07T14:05:38.1261015+00:00"}, "04xCbfeiK361SvdqZ93vWsbRSCE2LAeNqhOIOnz4/AY=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-09-07T14:05:38.1457421+00:00"}, "RPPkIMs4VriAJuSdn/mRB2Ld/fBxnUDkQvnI0BmhMh8=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-09-07T14:05:38.1582921+00:00"}, "bwpSwNYMxIAlpKfyxjCpV74LXr46ej2wfYAdL0L/HGg=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-09-07T14:05:38.1699945+00:00"}, "Odo5xklBcaO0IvvbQGQ/BERw4TomeEiwRaNE8S3n0Sg=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/LICENSE.md", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-09-07T14:05:37.7393633+00:00"}, "xG5YtYunQp1PgsF0GiF7P35KtjI69Xx4ZM4ed/sI5TU=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.js", "FileLength": 285314, "LastWriteTime": "2025-09-07T14:05:37.9052776+00:00"}, "fe8I5zfeZ37b4e+xU1IF8JHXtb3H6n5U0FSSw0S3C78=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-09-07T14:05:37.94872+00:00"}, "aRIho7CJGPtDQbep6gBYmcdje5MrFLROHC+auyMZY1c=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-09-07T14:05:37.9637174+00:00"}, "ac3caHPPokd+2+CnWlZVwglM9tJOy063Vl/K9dwmrtY=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-09-07T14:05:37.9877156+00:00"}, "pfqcMLB6XlHvPFBywS7qKjbhEbDhog7jjuGWQxzzX+A=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-09-07T14:05:38.0809853+00:00"}, "V6q4Yg1Jim9/1qEOU2yCcvXD+WeDBW9DL+EMQZSX2wc=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-09-07T14:05:38.1073007+00:00"}, "qIdQ9cmSVKr9eDFurFnTOeKY7S4PcmnxKY5lWgFUNd0=": {"Identity": "/App/SampleApp/wwwroot/lib/jquery/LICENSE.txt", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-09-07T14:05:37.711769+00:00"}}, "CachedCopyCandidates": {}}