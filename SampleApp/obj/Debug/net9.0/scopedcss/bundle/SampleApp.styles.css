/* _content/SampleApp/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-ymkhq9uu60] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-ymkhq9uu60] {
  color: #0077cc;
}

.btn-primary[b-ymkhq9uu60] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-ymkhq9uu60], .nav-pills .show > .nav-link[b-ymkhq9uu60] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-ymkhq9uu60] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-ymkhq9uu60] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-ymkhq9uu60] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-ymkhq9uu60] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-ymkhq9uu60] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
