{"Version": 1, "Hash": "NkGc5+9qbQRnB+u6mZM52I45ZCgWXUrfc//csiyTtwY=", "Source": "SampleApp", "BasePath": "_content/SampleApp", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "SampleApp/wwwroot", "Source": "SampleApp", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "Pattern": "**"}], "Assets": [{"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/02vjf4n6ru-0j3bgjxly4.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dvmyza30ke", "Integrity": "SUM9WWxVgf0VNNBf9Zf7iga7Z4tkGvbKAz0ev6eeJO0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/0ic3yl05sj-1b6d9wnjpo.gz", "SourceId": "SampleApp", "SourceType": "Computed", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "SampleApp#[.{fingerprint=1b6d9wnjpo}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/obj/Debug/net9.0/scopedcss/bundle/SampleApp.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dpcmenmtd1", "Integrity": "0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/obj/Debug/net9.0/scopedcss/bundle/SampleApp.styles.css", "FileLength": 535, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/0k6pkx7lef-vr1egmr9el.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lqx0uptmf7", "Integrity": "MyNLSRohJhqvR3grR9ZgvgrxU2FqeUMfO4gA3BNa/Tw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/0so9lvhu00-37tfw0ft22.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9cwgz03a4k", "Integrity": "ubq8orZ6fCxhzuD2xAJWPh7of4RUz1ZC+LZBWgNXDCM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/1lckc8904z-dxx9fxp4il.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "83ibl6bdqg", "Integrity": "VC2bV11abiRbZU+opSenUTXUAibJ8wP+8eKJvad/6m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/2ht3mbycqk-6pdc2jztkx.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f47el4ako4", "Integrity": "j4C6/l5/VktBAGO2tzhvkg2On432spHGetOb0zM/lng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/49ru4jfgx1-2z0ns9nrw6.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yeuxext30b", "Integrity": "Opr5hmrLHeEoBUrMugSdt/Vxh7nPIrdbPnxEe2XaKoA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/4en3v8dh7n-6cfz1n2cew.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iks902n20r", "Integrity": "4jD/MJ8V1KpkqYUhwGHEP96bA1HG/EKzzdID2Y/XFaY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/4lkxi7qll4-mlv21k5csn.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "my2pbmxgqc", "Integrity": "Pu7EEldYFfJduO8Z+fI/nS9U6SNQun+UzT1IrRHJ4oA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/5wyabwbwqr-0i3buxo5is.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0cxuflfi36", "Integrity": "wKh8lMNTtLKwUKNMLP8uDqnSQs/rLxp7Le9dXpyys9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.js", "FileLength": 84431, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/9exndt4m2s-r4e9w2rdcm.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zpcvsmmi2i", "Integrity": "J+aDgW/XAfgjCVwwYP82sXB0u8H3CBj5baCGeyPVq/w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/9lpbv05n85-lzl9nlhx6b.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b61onlgcie", "Integrity": "8wakphbzjcA3gIBAEAcZh4CWWnS9t3pv10WlHgDBKAQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/aezf3cup91-47otxtyo56.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "twtrqilhqb", "Integrity": "LUJC/c/lAEUqUN5H7xI/zyKVpIQFL/ghfMpMmc8OywY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/aki68h6fc0-fvhpjtyr6v.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q98uu36txx", "Integrity": "Xu/ywItCfSxVbbxuEI0ITLuPgYoQIGDVe13YkeJ/nuw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/andlqkz2yp-ag7o75518u.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aq3nh51dc0", "Integrity": "XlgSExzxNa3nkGlIRQzYeUGryaFcdCYYmDjJzMcVoSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/as3oy0t459-ub07r2b239.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xmt4dzeh8b", "Integrity": "99fh+Ouc0FukemvqpWJthoZTMmr1ZfsWXS8Eko1mo9U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/atiqvxx82x-bqjiyaj88i.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t6jyow2ejt", "Integrity": "pc3wV8tEXyJOebR7ZU/awGdi9J8M1YSpOQ0GfmB0jsI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/bbak4e5ife-s35ty4nyc5.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkyby1hkov", "Integrity": "zZkLTFGBa+N46XqOQpLIuz3qQ8TVabui1jCD1zzhqyg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/daradmyshj-mrlpezrjn3.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8vc9x<PERSON><PERSON><PERSON>", "Integrity": "nAkd+bXDCLqrA9jmHlM5YCYXVOPFw+/pJ2IBWBBluZc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/dworxm3kqg-lcd1t2u6c8.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bra8xlaz3i", "Integrity": "iYKYqA8UQ1ihqJMeu/hJ+eD7QXjXkTCIlDpMYpLPj68=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/e6wheytmsa-356vix0kms.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fo9enwqfr", "Integrity": "HxJunWv7ekzhB184/5lStP91qJcW7XRDqOATbPYdAB0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/elf0fbbvwd-ee0r1s7dh0.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0yn3oohjbt", "Integrity": "CBx5dddLjL6jyZIWwZ8xwYxsoJUQfgtgVh+b5XgAUJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/eqp54b8lr2-ausgxo2sd3.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "llvdsfuo7y", "Integrity": "xwbgJufxIF+fKh7W7rJOriFSpnA6Z1e0dGLZ8xZoFf4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/f9vqtch99f-xtxxf3hu2r.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/js/site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wzaqhcazp9", "Integrity": "LHuc18mXYNZ0bRgJHGLPvUJogHOb9WPItN01M/KFqj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/js/site.js", "FileLength": 189, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/g9ngfeipjr-87fc7y1x7t.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xqbkylpnx5", "Integrity": "eZ5ql6+ipm5O69S+f/4DgMADzzYHAfKSgSmm36kNWC4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/gjniralk8i-notf2xhcfb.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ug42gx2397", "Integrity": "4mjnv8wEsIPqFZ44yOygGYlGftJdqqFxCTXXzEYGCTs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/hcke43rrfq-61n19gt1b8.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gjtj35e07v", "Integrity": "tOy5kS1PTkjkEg7bjV8bJa0VpoEZJUHnbqYmm1YyhMg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/jm17pwaskg-j5mq2jizvt.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y05hho4joi", "Integrity": "TrtVB/NKd5KHvPHeEXAr18Yfbhc4otb6oAcl2TxPv2k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/jt69d3n297-c2jlpeoesf.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qv53nyrc9m", "Integrity": "etADIvPQ++XM7GLq9OLsigatXdZlEKhhquv3EENwXYM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/kwkfx39rnf-v0zj4ognzu.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y6rpgaobwt", "Integrity": "FvJuSMP2YMvmC+BvG+l+4oKlt+d41a9tXVE5TaIaicc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/l60lrw0jk5-b7pk76d08c.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xjqcbp9bzb", "Integrity": "bVBAValmreEE8qqeSbiezAvxjVdi8uEUBlZ8VQkpdxY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/lf8j8j6t8p-jd9uben2k1.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xzyp91gvov", "Integrity": "k3WNqhVR7f6rfrJtq9VFbqv+m7u1fGufHTgjssmCQIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/lkfe2vxgb6-ft3s53vfgj.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mapa9s0nlq", "Integrity": "jveMlVd5N9Rv8Y2xeGiEKZDcfCnnAYIyis0noH4HRbM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/mafedc6m7k-pj5nd1wqec.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ieclp98g0", "Integrity": "bZ0mhRTesxtxdVxduHjChjIuMo+bNzO6g++31seutGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/msqrmfzi2e-cosvhxvwiu.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hwotdmx7ke", "Integrity": "r8dihBWtRuflA1SshImDNVwNxupE3I4+paoNH5v5y2g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/n3edm74k0u-rzd6atqjts.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yiofxmsa8v", "Integrity": "Fr0UmnGwfb79O1UiS2iBYDv5MP+VyalRS+prbPLMzus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/o5s4nqgah0-x0q3zqp4vz.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s3lpkwkcby", "Integrity": "6HStD59bjkTPjQ3fGm7jnZcqoab/ANf+vA0DdOBKEcQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/ouocgudph5-tdbxkamptv.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ticab1qw3x", "Integrity": "G3NNvGGd9NifdVm4J+4bJhb/NfQMnJdBuYCr9yTMF74=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/p5g9so0ja5-1b6d9wnjpo.gz", "SourceId": "SampleApp", "SourceType": "Computed", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "SampleApp#[.{fingerprint=1b6d9wnjpo}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/obj/Debug/net9.0/scopedcss/projectbundle/SampleApp.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dpcmenmtd1", "Integrity": "0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/obj/Debug/net9.0/scopedcss/projectbundle/SampleApp.bundle.scp.css", "FileLength": 535, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/p6wbg186eq-d7shbmvgxk.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "de3myh4ugx", "Integrity": "Sz/4vyRFDomC/KgO1iIBNyVxkaoI5KEWCsMoGbjpAbo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/p7i2eirjm6-aexeepp0ev.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x19euzbaop", "Integrity": "VZ//8tsmm/peht1yvc7BlCC2l9jykWxgolFNNBRq3iA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/plgeurwc01-b9sayid5wm.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "css/site#[.{fingerprint=b9sayid5wm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/css/site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pi0kzaqg9p", "Integrity": "X7WdvJWe5+793Q+I1aPv6O/n2+XRWJsByQEd8dEKmGs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/css/site.css", "FileLength": 318, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/prk5ors1wn-muycvpuwrr.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tv1px50b4e", "Integrity": "N3WCKGekGDic3HiyYi1ZwSMVPxgAzjd22gI/cZzwGGs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/pzwa7d5815-y7v9cxd14o.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hf8l0l1abk", "Integrity": "E94YLFAwUcOKC0fKHFJ+2k6fv156l8Ftxru1cbrNzvA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/q603qz3b4p-hrwsygsryq.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zsi2r52cm2", "Integrity": "sxrCEPizO/oGb+PjbNzNkSY01EmjjnTghVkyX4PcaU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/qjyl94vguq-kbrnm935zg.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kfnqxfmd53", "Integrity": "IPal6iEIeXY+oO++FL+ujAbaZz2DQejhgt8x9Fw/Lyc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/rmsmp3ev4v-c2oey78nd0.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a4emzht0ak", "Integrity": "+qfEf74fYZcxGlJxLRXw29QR/dKmIyxbYFB8o0niDIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/s688x79q6z-46ein0sx1k.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ta814kukfc", "Integrity": "RRS8nI5OD8lm+2LTe3CimMlA3c6b5+JKzJ4B6FpGYuQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/sfdtm3yt31-ttgo8qnofa.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vqao1ymr7h", "Integrity": "Z9Xr9hTrQYEAWUwvg7CyNKwm+JkmM5dZcep0R6u6EHU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/sj0o4f8544-4v8eqarkd7.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kvtlytiqyp", "Integrity": "gCDjtbmTQ7oHsIcHiLvIK84vX7beVyMV9KBKNX7RvYA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/ss4tkdj3c0-493y06b0oq.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d1pecmyxtf", "Integrity": "QXrX67dKCMdhIT6OvT0zgftz+wu2XSxjyBlMsmIENQQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/tekqbeg4ih-k8d9w2qqmf.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8basbbqy9z", "Integrity": "NDo0uUYPt107zSN2+GQq0MWUIdA4tbBWTy3B2T5mt0g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/tgk972j05l-khv3u5hwcm.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lb2ax2er70", "Integrity": "vyx7RcdwvnTfx70lnbZkyl9ZtPX6H0Wzw0U66Wg/Ih0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/whrh3jxfo3-h1s4sie4z3.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w5smaoxcji", "Integrity": "FPIWN2C69yIYQwtPYEzfaF2VJOQ8E/FmHREomNVoHHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/wiryyv2z2z-erw9l3u2r3.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nplkdnog75", "Integrity": "Fey2Qnt9L6qRCjDsSyHEc+hUYSLpk1VpOMVLtOWQkPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/wx2frofdgn-iovd86k7lj.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rzoepyx18s", "Integrity": "+bjzmfX5lPuCupxKWq/ohX9O3Fq7iwK8faGFiD3QssA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/wzez5j1x5t-nvvlpmu67g.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yxaxjpj2zo", "Integrity": "plx2oQEeR60jH0/b817B21lxJBcijHB9tLUu8ZWE0IM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/x6mkgld0db-jj8uyg4cgr.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xl0jhl71e6", "Integrity": "1V6+yFJIywtUmypyWHCj13e/hMbrvGjWF7AtHTj7y88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/xcpntce7vg-63fj8s7r0e.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7355urbamp", "Integrity": "/fzN5m4HpCinhQgyhv9a2GoLOChbhOzS2FxhpXWIfeE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/ylttrv3j1o-83jwlth58m.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4oqpnpgpyd", "Integrity": "RA6FnFwvZwcy7PIS40oCJIxSKEHPVQl0ukyGVULfdIM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/yt0a7p7v4l-fsbi9cje9m.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qs39geit3q", "Integrity": "euP8e7V1Zn9c5ax4QOLwaTIFdDnD1FwYWI3wM9m58To=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/z6ylnxt33e-06098lyss8.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ivlimefe9h", "Integrity": "Pn08maprrhw5DTrqh4newsQpePUCfx9fxijw/Eq0FeU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/zclkhg7zcn-o1o13a6vjx.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pjwk5xzizl", "Integrity": "OyU7TDIA0tTFKi3MRwhEaOd0kz4cUNzCtNouAea7Wq0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/compressed/zjpzmircc6-pk9g2wxc8p.gz", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/compressed/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "utzeln9p1v", "Integrity": "HpKduG0LPCnTB73WyDjV1XJiA2n55vxAdfz5+N+Wlns=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/scopedcss/bundle/SampleApp.styles.css", "SourceId": "SampleApp", "SourceType": "Computed", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/scopedcss/bundle/", "BasePath": "_content/SampleApp", "RelativePath": "SampleApp#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "1b6d9wnjpo", "Integrity": "LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/obj/Debug/net9.0/scopedcss/bundle/SampleApp.styles.css", "FileLength": 1078, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/obj/Debug/net9.0/scopedcss/projectbundle/SampleApp.bundle.scp.css", "SourceId": "SampleApp", "SourceType": "Computed", "ContentRoot": "/App/SampleApp/obj/Debug/net9.0/scopedcss/projectbundle/", "BasePath": "_content/SampleApp", "RelativePath": "SampleApp#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "1b6d9wnjpo", "Integrity": "LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/App/SampleApp/obj/Debug/net9.0/scopedcss/projectbundle/SampleApp.bundle.scp.css", "FileLength": 1078, "LastWriteTime": "2025-09-07T14:06:34+00:00"}, {"Identity": "/App/SampleApp/wwwroot/css/site.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/site.css", "FileLength": 667, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/favicon.ico", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/js/site.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/site.js", "FileLength": 231, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/bootstrap/LICENSE", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/LICENSE", "FileLength": 1153, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery-validation/LICENSE.md", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.js", "FileLength": 285314, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-09-07T14:05:37+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.js", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.map", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-09-07T14:05:38+00:00"}, {"Identity": "/App/SampleApp/wwwroot/lib/jquery/LICENSE.txt", "SourceId": "SampleApp", "SourceType": "Discovered", "ContentRoot": "/App/SampleApp/wwwroot/", "BasePath": "_content/SampleApp", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-09-07T14:05:37+00:00"}], "Endpoints": [{"Route": "css/site.b9sayid5wm.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/plgeurwc01-b9sayid5wm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003134796238"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "318"}, {"Name": "ETag", "Value": "\"X7WdvJWe5+793Q+I1aPv6O/n2+XRWJsByQEd8dEKmGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b9sayid5wm"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg="}]}, {"Route": "css/site.b9sayid5wm.css", "AssetFile": "/App/SampleApp/wwwroot/css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "667"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b9sayid5wm"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg="}]}, {"Route": "css/site.b9sayid5wm.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/plgeurwc01-b9sayid5wm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "318"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"X7WdvJWe5+793Q+I1aPv6O/n2+XRWJsByQEd8dEKmGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b9sayid5wm"}, {"Name": "label", "Value": "css/site.css.gz"}, {"Name": "integrity", "Value": "sha256-X7WdvJWe5+793Q+I1aPv6O/n2+XRWJsByQEd8dEKmGs="}]}, {"Route": "css/site.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/plgeurwc01-b9sayid5wm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003134796238"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "318"}, {"Name": "ETag", "Value": "\"X7WdvJWe5+793Q+I1aPv6O/n2+XRWJsByQEd8dEKmGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg="}]}, {"Route": "css/site.css", "AssetFile": "/App/SampleApp/wwwroot/css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "667"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg="}]}, {"Route": "css/site.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/plgeurwc01-b9sayid5wm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "318"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"X7WdvJWe5+793Q+I1aPv6O/n2+XRWJsByQEd8dEKmGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X7WdvJWe5+793Q+I1aPv6O/n2+XRWJsByQEd8dEKmGs="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/hcke43rrfq-61n19gt1b8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "ETag", "Value": "\"tOy5kS1PTkjkEg7bjV8bJa0VpoEZJUHnbqYmm1YyhMg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "/App/SampleApp/wwwroot/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.61n19gt1b8.ico.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/hcke43rrfq-61n19gt1b8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"tOy5kS1PTkjkEg7bjV8bJa0VpoEZJUHnbqYmm1YyhMg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico.gz"}, {"Name": "integrity", "Value": "sha256-tOy5kS1PTkjkEg7bjV8bJa0VpoEZJUHnbqYmm1YyhMg="}]}, {"Route": "favicon.ico", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/hcke43rrfq-61n19gt1b8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "ETag", "Value": "\"tOy5kS1PTkjkEg7bjV8bJa0VpoEZJUHnbqYmm1YyhMg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "/App/SampleApp/wwwroot/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/hcke43rrfq-61n19gt1b8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"tOy5kS1PTkjkEg7bjV8bJa0VpoEZJUHnbqYmm1YyhMg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tOy5kS1PTkjkEg7bjV8bJa0VpoEZJUHnbqYmm1YyhMg="}]}, {"Route": "js/site.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/f9vqtch99f-xtxxf3hu2r.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.005263157895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "ETag", "Value": "\"LHuc18mXYNZ0bRgJHGLPvUJogHOb9WPItN01M/KFqj0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.js", "AssetFile": "/App/SampleApp/wwwroot/js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/f9vqtch99f-xtxxf3hu2r.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LHuc18mXYNZ0bRgJHGLPvUJogHOb9WPItN01M/KFqj0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LHuc18mXYNZ0bRgJHGLPvUJogHOb9WPItN01M/KFqj0="}]}, {"Route": "js/site.xtxxf3hu2r.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/f9vqtch99f-xtxxf3hu2r.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.005263157895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "ETag", "Value": "\"LHuc18mXYNZ0bRgJHGLPvUJogHOb9WPItN01M/KFqj0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.xtxxf3hu2r.js", "AssetFile": "/App/SampleApp/wwwroot/js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.xtxxf3hu2r.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/f9vqtch99f-xtxxf3hu2r.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LHuc18mXYNZ0bRgJHGLPvUJogHOb9WPItN01M/KFqj0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "label", "Value": "js/site.js.gz"}, {"Name": "integrity", "Value": "sha256-LHuc18mXYNZ0bRgJHGLPvUJogHOb9WPItN01M/KFqj0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/atiqvxx82x-bqjiyaj88i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148235992"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "ETag", "Value": "\"pc3wV8tEXyJOebR7ZU/awGdi9J8M1YSpOQ0GfmB0jsI=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70329"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/atiqvxx82x-bqjiyaj88i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pc3wV8tEXyJOebR7ZU/awGdi9J8M1YSpOQ0GfmB0jsI=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.gz"}, {"Name": "integrity", "Value": "sha256-pc3wV8tEXyJOebR7ZU/awGdi9J8M1YSpOQ0GfmB0jsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/atiqvxx82x-bqjiyaj88i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148235992"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "ETag", "Value": "\"pc3wV8tEXyJOebR7ZU/awGdi9J8M1YSpOQ0GfmB0jsI=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70329"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/jt69d3n297-c2jlpeoesf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "ETag", "Value": "\"etADIvPQ++XM7GLq9OLsigatXdZlEKhhquv3EENwXYM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/jt69d3n297-c2jlpeoesf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"etADIvPQ++XM7GLq9OLsigatXdZlEKhhquv3EENwXYM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz"}, {"Name": "integrity", "Value": "sha256-etADIvPQ++XM7GLq9OLsigatXdZlEKhhquv3EENwXYM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/atiqvxx82x-bqjiyaj88i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pc3wV8tEXyJOebR7ZU/awGdi9J8M1YSpOQ0GfmB0jsI=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pc3wV8tEXyJOebR7ZU/awGdi9J8M1YSpOQ0GfmB0jsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/jt69d3n297-c2jlpeoesf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "ETag", "Value": "\"etADIvPQ++XM7GLq9OLsigatXdZlEKhhquv3EENwXYM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/jt69d3n297-c2jlpeoesf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"etADIvPQ++XM7GLq9OLsigatXdZlEKhhquv3EENwXYM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-etADIvPQ++XM7GLq9OLsigatXdZlEKhhquv3EENwXYM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wiryyv2z2z-erw9l3u2r3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167504188"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "ETag", "Value": "\"Fey2Qnt9L6qRCjDsSyHEc+hUYSLpk1VpOMVLtOWQkPE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51795"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p7i2eirjm6-aexeepp0ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "ETag", "Value": "\"VZ//8tsmm/peht1yvc7BlCC2l9jykWxgolFNNBRq3iA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p7i2eirjm6-aexeepp0ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VZ//8tsmm/peht1yvc7BlCC2l9jykWxgolFNNBRq3iA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-VZ//8tsmm/peht1yvc7BlCC2l9jykWxgolFNNBRq3iA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wiryyv2z2z-erw9l3u2r3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fey2Qnt9L6qRCjDsSyHEc+hUYSLpk1VpOMVLtOWQkPE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fey2Qnt9L6qRCjDsSyHEc+hUYSLpk1VpOMVLtOWQkPE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p7i2eirjm6-aexeepp0ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "ETag", "Value": "\"VZ//8tsmm/peht1yvc7BlCC2l9jykWxgolFNNBRq3iA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p7i2eirjm6-aexeepp0ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VZ//8tsmm/peht1yvc7BlCC2l9jykWxgolFNNBRq3iA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VZ//8tsmm/peht1yvc7BlCC2l9jykWxgolFNNBRq3iA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wiryyv2z2z-erw9l3u2r3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167504188"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "ETag", "Value": "\"Fey2Qnt9L6qRCjDsSyHEc+hUYSLpk1VpOMVLtOWQkPE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51795"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wiryyv2z2z-erw9l3u2r3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fey2Qnt9L6qRCjDsSyHEc+hUYSLpk1VpOMVLtOWQkPE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz"}, {"Name": "integrity", "Value": "sha256-Fey2Qnt9L6qRCjDsSyHEc+hUYSLpk1VpOMVLtOWQkPE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p6wbg186eq-d7shbmvgxk.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148148148"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "ETag", "Value": "\"Sz/4vyRFDomC/KgO1iIBNyVxkaoI5KEWCsMoGbjpAbo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70403"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/eqp54b8lr2-ausgxo2sd3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "ETag", "Value": "\"xwbgJufxIF+fKh7W7rJOriFSpnA6Z1e0dGLZ8xZoFf4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/eqp54b8lr2-ausgxo2sd3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwbgJufxIF+fKh7W7rJOriFSpnA6Z1e0dGLZ8xZoFf4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-xwbgJufxIF+fKh7W7rJOriFSpnA6Z1e0dGLZ8xZoFf4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p6wbg186eq-d7shbmvgxk.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Sz/4vyRFDomC/KgO1iIBNyVxkaoI5KEWCsMoGbjpAbo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Sz/4vyRFDomC/KgO1iIBNyVxkaoI5KEWCsMoGbjpAbo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/eqp54b8lr2-ausgxo2sd3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "ETag", "Value": "\"xwbgJufxIF+fKh7W7rJOriFSpnA6Z1e0dGLZ8xZoFf4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/eqp54b8lr2-ausgxo2sd3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwbgJufxIF+fKh7W7rJOriFSpnA6Z1e0dGLZ8xZoFf4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xwbgJufxIF+fKh7W7rJOriFSpnA6Z1e0dGLZ8xZoFf4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p6wbg186eq-d7shbmvgxk.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148148148"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "ETag", "Value": "\"Sz/4vyRFDomC/KgO1iIBNyVxkaoI5KEWCsMoGbjpAbo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70403"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p6wbg186eq-d7shbmvgxk.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Sz/4vyRFDomC/KgO1iIBNyVxkaoI5KEWCsMoGbjpAbo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-Sz/4vyRFDomC/KgO1iIBNyVxkaoI5KEWCsMoGbjpAbo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/tekqbeg4ih-k8d9w2qqmf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167448091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "ETag", "Value": "\"NDo0uUYPt107zSN2+GQq0MWUIdA4tbBWTy3B2T5mt0g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/msqrmfzi2e-cosvhxvwiu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "ETag", "Value": "\"r8dihBWtRuflA1SshImDNVwNxupE3I4+paoNH5v5y2g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/msqrmfzi2e-cosvhxvwiu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"r8dihBWtRuflA1SshImDNVwNxupE3I4+paoNH5v5y2g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-r8dihBWtRuflA1SshImDNVwNxupE3I4+paoNH5v5y2g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/tekqbeg4ih-k8d9w2qqmf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NDo0uUYPt107zSN2+GQq0MWUIdA4tbBWTy3B2T5mt0g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NDo0uUYPt107zSN2+GQq0MWUIdA4tbBWTy3B2T5mt0g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/msqrmfzi2e-cosvhxvwiu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "ETag", "Value": "\"r8dihBWtRuflA1SshImDNVwNxupE3I4+paoNH5v5y2g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/msqrmfzi2e-cosvhxvwiu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"r8dihBWtRuflA1SshImDNVwNxupE3I4+paoNH5v5y2g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r8dihBWtRuflA1SshImDNVwNxupE3I4+paoNH5v5y2g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/tekqbeg4ih-k8d9w2qqmf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167448091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "ETag", "Value": "\"NDo0uUYPt107zSN2+GQq0MWUIdA4tbBWTy3B2T5mt0g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/tekqbeg4ih-k8d9w2qqmf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NDo0uUYPt107zSN2+GQq0MWUIdA4tbBWTy3B2T5mt0g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-NDo0uUYPt107zSN2+GQq0MWUIdA4tbBWTy3B2T5mt0g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/as3oy0t459-ub07r2b239.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000295770482"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "ETag", "Value": "\"99fh+Ouc0FukemvqpWJthoZTMmr1ZfsWXS8Eko1mo9U=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/aki68h6fc0-fvhpjtyr6v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "ETag", "Value": "\"Xu/ywItCfSxVbbxuEI0ITLuPgYoQIGDVe13YkeJ/nuw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/aki68h6fc0-fvhpjtyr6v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xu/ywItCfSxVbbxuEI0ITLuPgYoQIGDVe13YkeJ/nuw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz"}, {"Name": "integrity", "Value": "sha256-Xu/ywItCfSxVbbxuEI0ITLuPgYoQIGDVe13YkeJ/nuw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/as3oy0t459-ub07r2b239.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"99fh+Ouc0FukemvqpWJthoZTMmr1ZfsWXS8Eko1mo9U=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-99fh+Ouc0FukemvqpWJthoZTMmr1ZfsWXS8Eko1mo9U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/aki68h6fc0-fvhpjtyr6v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "ETag", "Value": "\"Xu/ywItCfSxVbbxuEI0ITLuPgYoQIGDVe13YkeJ/nuw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/aki68h6fc0-fvhpjtyr6v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xu/ywItCfSxVbbxuEI0ITLuPgYoQIGDVe13YkeJ/nuw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xu/ywItCfSxVbbxuEI0ITLuPgYoQIGDVe13YkeJ/nuw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/l60lrw0jk5-b7pk76d08c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311138768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "ETag", "Value": "\"bVBAValmreEE8qqeSbiezAvxjVdi8uEUBlZ8VQkpdxY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/l60lrw0jk5-b7pk76d08c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bVBAValmreEE8qqeSbiezAvxjVdi8uEUBlZ8VQkpdxY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz"}, {"Name": "integrity", "Value": "sha256-bVBAValmreEE8qqeSbiezAvxjVdi8uEUBlZ8VQkpdxY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/l60lrw0jk5-b7pk76d08c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311138768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "ETag", "Value": "\"bVBAValmreEE8qqeSbiezAvxjVdi8uEUBlZ8VQkpdxY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/yt0a7p7v4l-fsbi9cje9m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "ETag", "Value": "\"euP8e7V1Zn9c5ax4QOLwaTIFdDnD1FwYWI3wM9m58To=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/yt0a7p7v4l-fsbi9cje9m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"euP8e7V1Zn9c5ax4QOLwaTIFdDnD1FwYWI3wM9m58To=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-euP8e7V1Zn9c5ax4QOLwaTIFdDnD1FwYWI3wM9m58To="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/l60lrw0jk5-b7pk76d08c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bVBAValmreEE8qqeSbiezAvxjVdi8uEUBlZ8VQkpdxY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bVBAValmreEE8qqeSbiezAvxjVdi8uEUBlZ8VQkpdxY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/yt0a7p7v4l-fsbi9cje9m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "ETag", "Value": "\"euP8e7V1Zn9c5ax4QOLwaTIFdDnD1FwYWI3wM9m58To=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/yt0a7p7v4l-fsbi9cje9m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"euP8e7V1Zn9c5ax4QOLwaTIFdDnD1FwYWI3wM9m58To=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-euP8e7V1Zn9c5ax4QOLwaTIFdDnD1FwYWI3wM9m58To="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/n3edm74k0u-rzd6atqjts.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000296912114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "ETag", "Value": "\"Fr0UmnGwfb79O1UiS2iBYDv5MP+VyalRS+prbPLMzus=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12058"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/elf0fbbvwd-ee0r1s7dh0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "ETag", "Value": "\"CBx5dddLjL6jyZIWwZ8xwYxsoJUQfgtgVh+b5XgAUJM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/elf0fbbvwd-ee0r1s7dh0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CBx5dddLjL6jyZIWwZ8xwYxsoJUQfgtgVh+b5XgAUJM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-CBx5dddLjL6jyZIWwZ8xwYxsoJUQfgtgVh+b5XgAUJM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/n3edm74k0u-rzd6atqjts.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fr0UmnGwfb79O1UiS2iBYDv5MP+VyalRS+prbPLMzus=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fr0UmnGwfb79O1UiS2iBYDv5MP+VyalRS+prbPLMzus="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/elf0fbbvwd-ee0r1s7dh0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "ETag", "Value": "\"CBx5dddLjL6jyZIWwZ8xwYxsoJUQfgtgVh+b5XgAUJM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/elf0fbbvwd-ee0r1s7dh0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CBx5dddLjL6jyZIWwZ8xwYxsoJUQfgtgVh+b5XgAUJM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CBx5dddLjL6jyZIWwZ8xwYxsoJUQfgtgVh+b5XgAUJM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/1lckc8904z-dxx9fxp4il.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307976594"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "ETag", "Value": "\"VC2bV11abiRbZU+opSenUTXUAibJ8wP+8eKJvad/6m4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/1lckc8904z-dxx9fxp4il.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VC2bV11abiRbZU+opSenUTXUAibJ8wP+8eKJvad/6m4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VC2bV11abiRbZU+opSenUTXUAibJ8wP+8eKJvad/6m4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/lf8j8j6t8p-jd9uben2k1.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "ETag", "Value": "\"k3WNqhVR7f6rfrJtq9VFbqv+m7u1fGufHTgjssmCQIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/lf8j8j6t8p-jd9uben2k1.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k3WNqhVR7f6rfrJtq9VFbqv+m7u1fGufHTgjssmCQIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-k3WNqhVR7f6rfrJtq9VFbqv+m7u1fGufHTgjssmCQIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/lf8j8j6t8p-jd9uben2k1.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "ETag", "Value": "\"k3WNqhVR7f6rfrJtq9VFbqv+m7u1fGufHTgjssmCQIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/lf8j8j6t8p-jd9uben2k1.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k3WNqhVR7f6rfrJtq9VFbqv+m7u1fGufHTgjssmCQIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k3WNqhVR7f6rfrJtq9VFbqv+m7u1fGufHTgjssmCQIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/1lckc8904z-dxx9fxp4il.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307976594"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "ETag", "Value": "\"VC2bV11abiRbZU+opSenUTXUAibJ8wP+8eKJvad/6m4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/1lckc8904z-dxx9fxp4il.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VC2bV11abiRbZU+opSenUTXUAibJ8wP+8eKJvad/6m4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-VC2bV11abiRbZU+opSenUTXUAibJ8wP+8eKJvad/6m4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/n3edm74k0u-rzd6atqjts.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000296912114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "ETag", "Value": "\"Fr0UmnGwfb79O1UiS2iBYDv5MP+VyalRS+prbPLMzus=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12058"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/n3edm74k0u-rzd6atqjts.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fr0UmnGwfb79O1UiS2iBYDv5MP+VyalRS+prbPLMzus=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-Fr0UmnGwfb79O1UiS2iBYDv5MP+VyalRS+prbPLMzus="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/as3oy0t459-ub07r2b239.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000295770482"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "ETag", "Value": "\"99fh+Ouc0FukemvqpWJthoZTMmr1ZfsWXS8Eko1mo9U=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/as3oy0t459-ub07r2b239.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"99fh+Ouc0FukemvqpWJthoZTMmr1ZfsWXS8Eko1mo9U=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz"}, {"Name": "integrity", "Value": "sha256-99fh+Ouc0FukemvqpWJthoZTMmr1ZfsWXS8Eko1mo9U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/tgk972j05l-khv3u5hwcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083388926"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "ETag", "Value": "\"vyx7RcdwvnTfx70lnbZkyl9ZtPX6H0Wzw0U66Wg/Ih0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/tgk972j05l-khv3u5hwcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vyx7RcdwvnTfx70lnbZkyl9ZtPX6H0Wzw0U66Wg/Ih0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vyx7RcdwvnTfx70lnbZkyl9ZtPX6H0Wzw0U66Wg/Ih0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/9exndt4m2s-r4e9w2rdcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "ETag", "Value": "\"J+aDgW/XAfgjCVwwYP82sXB0u8H3CBj5baCGeyPVq/w=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/9exndt4m2s-r4e9w2rdcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"J+aDgW/XAfgjCVwwYP82sXB0u8H3CBj5baCGeyPVq/w=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J+aDgW/XAfgjCVwwYP82sXB0u8H3CBj5baCGeyPVq/w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/9exndt4m2s-r4e9w2rdcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "ETag", "Value": "\"J+aDgW/XAfgjCVwwYP82sXB0u8H3CBj5baCGeyPVq/w=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/9exndt4m2s-r4e9w2rdcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"J+aDgW/XAfgjCVwwYP82sXB0u8H3CBj5baCGeyPVq/w=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz"}, {"Name": "integrity", "Value": "sha256-J+aDgW/XAfgjCVwwYP82sXB0u8H3CBj5baCGeyPVq/w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/tgk972j05l-khv3u5hwcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083388926"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "ETag", "Value": "\"vyx7RcdwvnTfx70lnbZkyl9ZtPX6H0Wzw0U66Wg/Ih0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/tgk972j05l-khv3u5hwcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vyx7RcdwvnTfx70lnbZkyl9ZtPX6H0Wzw0U66Wg/Ih0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz"}, {"Name": "integrity", "Value": "sha256-vyx7RcdwvnTfx70lnbZkyl9ZtPX6H0Wzw0U66Wg/Ih0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/dworxm3kqg-lcd1t2u6c8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090383225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "ETag", "Value": "\"iYKYqA8UQ1ihqJMeu/hJ+eD7QXjXkTCIlDpMYpLPj68=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85352"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/rmsmp3ev4v-c2oey78nd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "ETag", "Value": "\"+qfEf74fYZcxGlJxLRXw29QR/dKmIyxbYFB8o0niDIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/rmsmp3ev4v-c2oey78nd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+qfEf74fYZcxGlJxLRXw29QR/dKmIyxbYFB8o0niDIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-+qfEf74fYZcxGlJxLRXw29QR/dKmIyxbYFB8o0niDIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/dworxm3kqg-lcd1t2u6c8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iYKYqA8UQ1ihqJMeu/hJ+eD7QXjXkTCIlDpMYpLPj68=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iYKYqA8UQ1ihqJMeu/hJ+eD7QXjXkTCIlDpMYpLPj68="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/rmsmp3ev4v-c2oey78nd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "ETag", "Value": "\"+qfEf74fYZcxGlJxLRXw29QR/dKmIyxbYFB8o0niDIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/rmsmp3ev4v-c2oey78nd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+qfEf74fYZcxGlJxLRXw29QR/dKmIyxbYFB8o0niDIU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+qfEf74fYZcxGlJxLRXw29QR/dKmIyxbYFB8o0niDIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/dworxm3kqg-lcd1t2u6c8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090383225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "ETag", "Value": "\"iYKYqA8UQ1ihqJMeu/hJ+eD7QXjXkTCIlDpMYpLPj68=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85352"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/dworxm3kqg-lcd1t2u6c8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iYKYqA8UQ1ihqJMeu/hJ+eD7QXjXkTCIlDpMYpLPj68=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz"}, {"Name": "integrity", "Value": "sha256-iYKYqA8UQ1ihqJMeu/hJ+eD7QXjXkTCIlDpMYpLPj68="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ouocgudph5-tdbxkamptv.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083794201"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "ETag", "Value": "\"G3NNvGGd9NifdVm4J+4bJhb/NfQMnJdBuYCr9yTMF74=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ouocgudph5-tdbxkamptv.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"G3NNvGGd9NifdVm4J+4bJhb/NfQMnJdBuYCr9yTMF74=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G3NNvGGd9NifdVm4J+4bJhb/NfQMnJdBuYCr9yTMF74="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/jm17pwaskg-j5mq2jizvt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "ETag", "Value": "\"TrtVB/NKd5KHvPHeEXAr18Yfbhc4otb6oAcl2TxPv2k=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/jm17pwaskg-j5mq2jizvt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"TrtVB/NKd5KHvPHeEXAr18Yfbhc4otb6oAcl2TxPv2k=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-TrtVB/NKd5KHvPHeEXAr18Yfbhc4otb6oAcl2TxPv2k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/jm17pwaskg-j5mq2jizvt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "ETag", "Value": "\"TrtVB/NKd5KHvPHeEXAr18Yfbhc4otb6oAcl2TxPv2k=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/jm17pwaskg-j5mq2jizvt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"TrtVB/NKd5KHvPHeEXAr18Yfbhc4otb6oAcl2TxPv2k=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TrtVB/NKd5KHvPHeEXAr18Yfbhc4otb6oAcl2TxPv2k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/z6ylnxt33e-06098lyss8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090522314"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "ETag", "Value": "\"Pn08maprrhw5DTrqh4newsQpePUCfx9fxijw/Eq0FeU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85281"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/z6ylnxt33e-06098lyss8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Pn08maprrhw5DTrqh4newsQpePUCfx9fxijw/Eq0FeU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-Pn08maprrhw5DTrqh4newsQpePUCfx9fxijw/Eq0FeU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/z6ylnxt33e-06098lyss8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090522314"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "ETag", "Value": "\"Pn08maprrhw5DTrqh4newsQpePUCfx9fxijw/Eq0FeU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85281"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/z6ylnxt33e-06098lyss8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Pn08maprrhw5DTrqh4newsQpePUCfx9fxijw/Eq0FeU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Pn08maprrhw5DTrqh4newsQpePUCfx9fxijw/Eq0FeU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wzez5j1x5t-nvvlpmu67g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "ETag", "Value": "\"plx2oQEeR60jH0/b817B21lxJBcijHB9tLUu8ZWE0IM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wzez5j1x5t-nvvlpmu67g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"plx2oQEeR60jH0/b817B21lxJBcijHB9tLUu8ZWE0IM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-plx2oQEeR60jH0/b817B21lxJBcijHB9tLUu8ZWE0IM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wzez5j1x5t-nvvlpmu67g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "ETag", "Value": "\"plx2oQEeR60jH0/b817B21lxJBcijHB9tLUu8ZWE0IM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wzez5j1x5t-nvvlpmu67g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"plx2oQEeR60jH0/b817B21lxJBcijHB9tLUu8ZWE0IM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-plx2oQEeR60jH0/b817B21lxJBcijHB9tLUu8ZWE0IM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ouocgudph5-tdbxkamptv.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083794201"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "ETag", "Value": "\"G3NNvGGd9NifdVm4J+4bJhb/NfQMnJdBuYCr9yTMF74=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ouocgudph5-tdbxkamptv.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"G3NNvGGd9NifdVm4J+4bJhb/NfQMnJdBuYCr9yTMF74=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-G3NNvGGd9NifdVm4J+4bJhb/NfQMnJdBuYCr9yTMF74="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/bbak4e5ife-s35ty4nyc5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030073379"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "ETag", "Value": "\"zZkLTFGBa+N46XqOQpLIuz3qQ8TVabui1jCD1zzhqyg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "281046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/bbak4e5ife-s35ty4nyc5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zZkLTFGBa+N46XqOQpLIuz3qQ8TVabui1jCD1zzhqyg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zZkLTFGBa+N46XqOQpLIuz3qQ8TVabui1jCD1zzhqyg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/mafedc6m7k-pj5nd1wqec.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "ETag", "Value": "\"bZ0mhRTesxtxdVxduHjChjIuMo+bNzO6g++31seutGQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/mafedc6m7k-pj5nd1wqec.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bZ0mhRTesxtxdVxduHjChjIuMo+bNzO6g++31seutGQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bZ0mhRTesxtxdVxduHjChjIuMo+bNzO6g++31seutGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/mafedc6m7k-pj5nd1wqec.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "ETag", "Value": "\"bZ0mhRTesxtxdVxduHjChjIuMo+bNzO6g++31seutGQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/mafedc6m7k-pj5nd1wqec.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bZ0mhRTesxtxdVxduHjChjIuMo+bNzO6g++31seutGQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map.gz"}, {"Name": "integrity", "Value": "sha256-bZ0mhRTesxtxdVxduHjChjIuMo+bNzO6g++31seutGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/s688x79q6z-46ein0sx1k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032295569"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "ETag", "Value": "\"RRS8nI5OD8lm+2LTe3CimMlA3c6b5+JKzJ4B6FpGYuQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "46ein0sx1k"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "46ein0sx1k"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/s688x79q6z-46ein0sx1k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RRS8nI5OD8lm+2LTe3CimMlA3c6b5+JKzJ4B6FpGYuQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "46ein0sx1k"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-RRS8nI5OD8lm+2LTe3CimMlA3c6b5+JKzJ4B6FpGYuQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/s688x79q6z-46ein0sx1k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032295569"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "ETag", "Value": "\"RRS8nI5OD8lm+2LTe3CimMlA3c6b5+JKzJ4B6FpGYuQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/s688x79q6z-46ein0sx1k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RRS8nI5OD8lm+2LTe3CimMlA3c6b5+JKzJ4B6FpGYuQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RRS8nI5OD8lm+2LTe3CimMlA3c6b5+JKzJ4B6FpGYuQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/kwkfx39rnf-v0zj4ognzu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "ETag", "Value": "\"FvJuSMP2YMvmC+BvG+l+4oKlt+d41a9tXVE5TaIaicc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/kwkfx39rnf-v0zj4ognzu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FvJuSMP2YMvmC+BvG+l+4oKlt+d41a9tXVE5TaIaicc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FvJuSMP2YMvmC+BvG+l+4oKlt+d41a9tXVE5TaIaicc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/kwkfx39rnf-v0zj4ognzu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "ETag", "Value": "\"FvJuSMP2YMvmC+BvG+l+4oKlt+d41a9tXVE5TaIaicc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/kwkfx39rnf-v0zj4ognzu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FvJuSMP2YMvmC+BvG+l+4oKlt+d41a9tXVE5TaIaicc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-FvJuSMP2YMvmC+BvG+l+4oKlt+d41a9tXVE5TaIaicc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0so9lvhu00-37tfw0ft22.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030209655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "ETag", "Value": "\"ubq8orZ6fCxhzuD2xAJWPh7of4RUz1ZC+LZBWgNXDCM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "280259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0so9lvhu00-37tfw0ft22.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ubq8orZ6fCxhzuD2xAJWPh7of4RUz1ZC+LZBWgNXDCM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-ubq8orZ6fCxhzuD2xAJWPh7of4RUz1ZC+LZBWgNXDCM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0so9lvhu00-37tfw0ft22.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030209655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "ETag", "Value": "\"ubq8orZ6fCxhzuD2xAJWPh7of4RUz1ZC+LZBWgNXDCM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "280259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0so9lvhu00-37tfw0ft22.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ubq8orZ6fCxhzuD2xAJWPh7of4RUz1ZC+LZBWgNXDCM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ubq8orZ6fCxhzuD2xAJWPh7of4RUz1ZC+LZBWgNXDCM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/q603qz3b4p-hrwsygsryq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "ETag", "Value": "\"sxrCEPizO/oGb+PjbNzNkSY01EmjjnTghVkyX4PcaU8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/q603qz3b4p-hrwsygsryq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sxrCEPizO/oGb+PjbNzNkSY01EmjjnTghVkyX4PcaU8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-sxrCEPizO/oGb+PjbNzNkSY01EmjjnTghVkyX4PcaU8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/q603qz3b4p-hrwsygsryq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "ETag", "Value": "\"sxrCEPizO/oGb+PjbNzNkSY01EmjjnTghVkyX4PcaU8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/q603qz3b4p-hrwsygsryq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sxrCEPizO/oGb+PjbNzNkSY01EmjjnTghVkyX4PcaU8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sxrCEPizO/oGb+PjbNzNkSY01EmjjnTghVkyX4PcaU8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/zjpzmircc6-pk9g2wxc8p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032271598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "ETag", "Value": "\"HpKduG0LPCnTB73WyDjV1XJiA2n55vxAdfz5+N+Wlns=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/lkfe2vxgb6-ft3s53vfgj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "ETag", "Value": "\"jveMlVd5N9Rv8Y2xeGiEKZDcfCnnAYIyis0noH4HRbM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/lkfe2vxgb6-ft3s53vfgj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"jveMlVd5N9Rv8Y2xeGiEKZDcfCnnAYIyis0noH4HRbM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-jveMlVd5N9Rv8Y2xeGiEKZDcfCnnAYIyis0noH4HRbM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/zjpzmircc6-pk9g2wxc8p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpKduG0LPCnTB73WyDjV1XJiA2n55vxAdfz5+N+Wlns=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HpKduG0LPCnTB73WyDjV1XJiA2n55vxAdfz5+N+Wlns="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/lkfe2vxgb6-ft3s53vfgj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "ETag", "Value": "\"jveMlVd5N9Rv8Y2xeGiEKZDcfCnnAYIyis0noH4HRbM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/lkfe2vxgb6-ft3s53vfgj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"jveMlVd5N9Rv8Y2xeGiEKZDcfCnnAYIyis0noH4HRbM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jveMlVd5N9Rv8Y2xeGiEKZDcfCnnAYIyis0noH4HRbM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/zjpzmircc6-pk9g2wxc8p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032271598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "ETag", "Value": "\"HpKduG0LPCnTB73WyDjV1XJiA2n55vxAdfz5+N+Wlns=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/zjpzmircc6-pk9g2wxc8p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HpKduG0LPCnTB73WyDjV1XJiA2n55vxAdfz5+N+Wlns=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-HpKduG0LPCnTB73WyDjV1XJiA2n55vxAdfz5+N+Wlns="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/bbak4e5ife-s35ty4nyc5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030073379"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "ETag", "Value": "\"zZkLTFGBa+N46XqOQpLIuz3qQ8TVabui1jCD1zzhqyg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s35ty4nyc5"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "281046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s35ty4nyc5"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/bbak4e5ife-s35ty4nyc5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zZkLTFGBa+N46XqOQpLIuz3qQ8TVabui1jCD1zzhqyg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s35ty4nyc5"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.gz"}, {"Name": "integrity", "Value": "sha256-zZkLTFGBa+N46XqOQpLIuz3qQ8TVabui1jCD1zzhqyg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/4en3v8dh7n-6cfz1n2cew.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022545373"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "ETag", "Value": "\"4jD/MJ8V1KpkqYUhwGHEP96bA1HG/EKzzdID2Y/XFaY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "207819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/4en3v8dh7n-6cfz1n2cew.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jD/MJ8V1KpkqYUhwGHEP96bA1HG/EKzzdID2Y/XFaY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz"}, {"Name": "integrity", "Value": "sha256-4jD/MJ8V1KpkqYUhwGHEP96bA1HG/EKzzdID2Y/XFaY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/4en3v8dh7n-6cfz1n2cew.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022545373"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "ETag", "Value": "\"4jD/MJ8V1KpkqYUhwGHEP96bA1HG/EKzzdID2Y/XFaY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "207819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/2ht3mbycqk-6pdc2jztkx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010864133"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "ETag", "Value": "\"j4C6/l5/VktBAGO2tzhvkg2On432spHGetOb0zM/lng=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/2ht3mbycqk-6pdc2jztkx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j4C6/l5/VktBAGO2tzhvkg2On432spHGetOb0zM/lng=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz"}, {"Name": "integrity", "Value": "sha256-j4C6/l5/VktBAGO2tzhvkg2On432spHGetOb0zM/lng="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/4en3v8dh7n-6cfz1n2cew.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jD/MJ8V1KpkqYUhwGHEP96bA1HG/EKzzdID2Y/XFaY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jD/MJ8V1KpkqYUhwGHEP96bA1HG/EKzzdID2Y/XFaY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/2ht3mbycqk-6pdc2jztkx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010864133"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "ETag", "Value": "\"j4C6/l5/VktBAGO2tzhvkg2On432spHGetOb0zM/lng=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/2ht3mbycqk-6pdc2jztkx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j4C6/l5/VktBAGO2tzhvkg2On432spHGetOb0zM/lng=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j4C6/l5/VktBAGO2tzhvkg2On432spHGetOb0zM/lng="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ss4tkdj3c0-493y06b0oq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041692725"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "ETag", "Value": "\"QXrX67dKCMdhIT6OvT0zgftz+wu2XSxjyBlMsmIENQQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ss4tkdj3c0-493y06b0oq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QXrX67dKCMdhIT6OvT0zgftz+wu2XSxjyBlMsmIENQQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz"}, {"Name": "integrity", "Value": "sha256-QXrX67dKCMdhIT6OvT0zgftz+wu2XSxjyBlMsmIENQQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ss4tkdj3c0-493y06b0oq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041692725"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "ETag", "Value": "\"QXrX67dKCMdhIT6OvT0zgftz+wu2XSxjyBlMsmIENQQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ss4tkdj3c0-493y06b0oq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QXrX67dKCMdhIT6OvT0zgftz+wu2XSxjyBlMsmIENQQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QXrX67dKCMdhIT6OvT0zgftz+wu2XSxjyBlMsmIENQQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wx2frofdgn-iovd86k7lj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499937"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "ETag", "Value": "\"+bjzmfX5lPuCupxKWq/ohX9O3Fq7iwK8faGFiD3QssA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wx2frofdgn-iovd86k7lj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+bjzmfX5lPuCupxKWq/ohX9O3Fq7iwK8faGFiD3QssA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-+bjzmfX5lPuCupxKWq/ohX9O3Fq7iwK8faGFiD3QssA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wx2frofdgn-iovd86k7lj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499937"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "ETag", "Value": "\"+bjzmfX5lPuCupxKWq/ohX9O3Fq7iwK8faGFiD3QssA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/wx2frofdgn-iovd86k7lj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+bjzmfX5lPuCupxKWq/ohX9O3Fq7iwK8faGFiD3QssA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+bjzmfX5lPuCupxKWq/ohX9O3Fq7iwK8faGFiD3QssA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0k6pkx7lef-vr1egmr9el.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000034658441"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "ETag", "Value": "\"MyNLSRohJhqvR3grR9ZgvgrxU2FqeUMfO4gA3BNa/Tw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "135829"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0k6pkx7lef-vr1egmr9el.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MyNLSRohJhqvR3grR9ZgvgrxU2FqeUMfO4gA3BNa/Tw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MyNLSRohJhqvR3grR9ZgvgrxU2FqeUMfO4gA3BNa/Tw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/qjyl94vguq-kbrnm935zg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593083"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "ETag", "Value": "\"IPal6iEIeXY+oO++FL+ujAbaZz2DQejhgt8x9Fw/Lyc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/qjyl94vguq-kbrnm935zg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IPal6iEIeXY+oO++FL+ujAbaZz2DQejhgt8x9Fw/Lyc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz"}, {"Name": "integrity", "Value": "sha256-IPal6iEIeXY+oO++FL+ujAbaZz2DQejhgt8x9Fw/Lyc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/qjyl94vguq-kbrnm935zg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593083"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "ETag", "Value": "\"IPal6iEIeXY+oO++FL+ujAbaZz2DQejhgt8x9Fw/Lyc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/qjyl94vguq-kbrnm935zg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"IPal6iEIeXY+oO++FL+ujAbaZz2DQejhgt8x9Fw/Lyc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IPal6iEIeXY+oO++FL+ujAbaZz2DQejhgt8x9Fw/Lyc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/x6mkgld0db-jj8uyg4cgr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053659584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "ETag", "Value": "\"1V6+yFJIywtUmypyWHCj13e/hMbrvGjWF7AtHTj7y88=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "73935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/x6mkgld0db-jj8uyg4cgr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1V6+yFJIywtUmypyWHCj13e/hMbrvGjWF7AtHTj7y88=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz"}, {"Name": "integrity", "Value": "sha256-1V6+yFJIywtUmypyWHCj13e/hMbrvGjWF7AtHTj7y88="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/x6mkgld0db-jj8uyg4cgr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053659584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "ETag", "Value": "\"1V6+yFJIywtUmypyWHCj13e/hMbrvGjWF7AtHTj7y88=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "73935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/x6mkgld0db-jj8uyg4cgr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1V6+yFJIywtUmypyWHCj13e/hMbrvGjWF7AtHTj7y88=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1V6+yFJIywtUmypyWHCj13e/hMbrvGjWF7AtHTj7y88="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/pzwa7d5815-y7v9cxd14o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017646644"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "ETag", "Value": "\"E94YLFAwUcOKC0fKHFJ+2k6fv156l8Ftxru1cbrNzvA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/pzwa7d5815-y7v9cxd14o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"E94YLFAwUcOKC0fKHFJ+2k6fv156l8Ftxru1cbrNzvA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E94YLFAwUcOKC0fKHFJ+2k6fv156l8Ftxru1cbrNzvA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/pzwa7d5815-y7v9cxd14o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017646644"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "ETag", "Value": "\"E94YLFAwUcOKC0fKHFJ+2k6fv156l8Ftxru1cbrNzvA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/pzwa7d5815-y7v9cxd14o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"E94YLFAwUcOKC0fKHFJ+2k6fv156l8Ftxru1cbrNzvA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-E94YLFAwUcOKC0fKHFJ+2k6fv156l8Ftxru1cbrNzvA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0k6pkx7lef-vr1egmr9el.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000034658441"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "ETag", "Value": "\"MyNLSRohJhqvR3grR9ZgvgrxU2FqeUMfO4gA3BNa/Tw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "135829"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0k6pkx7lef-vr1egmr9el.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MyNLSRohJhqvR3grR9ZgvgrxU2FqeUMfO4gA3BNa/Tw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.gz"}, {"Name": "integrity", "Value": "sha256-MyNLSRohJhqvR3grR9ZgvgrxU2FqeUMfO4gA3BNa/Tw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/gjniralk8i-notf2xhcfb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000033818059"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "ETag", "Value": "\"4mjnv8wEsIPqFZ44yOygGYlGftJdqqFxCTXXzEYGCTs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145401"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/gjniralk8i-notf2xhcfb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4mjnv8wEsIPqFZ44yOygGYlGftJdqqFxCTXXzEYGCTs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mjnv8wEsIPqFZ44yOygGYlGftJdqqFxCTXXzEYGCTs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/whrh3jxfo3-h1s4sie4z3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015522166"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "ETag", "Value": "\"FPIWN2C69yIYQwtPYEzfaF2VJOQ8E/FmHREomNVoHHw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/whrh3jxfo3-h1s4sie4z3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FPIWN2C69yIYQwtPYEzfaF2VJOQ8E/FmHREomNVoHHw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map.gz"}, {"Name": "integrity", "Value": "sha256-FPIWN2C69yIYQwtPYEzfaF2VJOQ8E/FmHREomNVoHHw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/whrh3jxfo3-h1s4sie4z3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015522166"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "ETag", "Value": "\"FPIWN2C69yIYQwtPYEzfaF2VJOQ8E/FmHREomNVoHHw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/whrh3jxfo3-h1s4sie4z3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FPIWN2C69yIYQwtPYEzfaF2VJOQ8E/FmHREomNVoHHw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FPIWN2C69yIYQwtPYEzfaF2VJOQ8E/FmHREomNVoHHw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/xcpntce7vg-63fj8s7r0e.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000060106990"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "ETag", "Value": "\"/fzN5m4HpCinhQgyhv9a2GoLOChbhOzS2FxhpXWIfeE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/xcpntce7vg-63fj8s7r0e.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/fzN5m4HpCinhQgyhv9a2GoLOChbhOzS2FxhpXWIfeE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.gz"}, {"Name": "integrity", "Value": "sha256-/fzN5m4HpCinhQgyhv9a2GoLOChbhOzS2FxhpXWIfeE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/xcpntce7vg-63fj8s7r0e.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000060106990"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "ETag", "Value": "\"/fzN5m4HpCinhQgyhv9a2GoLOChbhOzS2FxhpXWIfeE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/02vjf4n6ru-0j3bgjxly4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017905424"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "ETag", "Value": "\"SUM9WWxVgf0VNNBf9Zf7iga7Z4tkGvbKAz0ev6eeJO0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/02vjf4n6ru-0j3bgjxly4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"SUM9WWxVgf0VNNBf9Zf7iga7Z4tkGvbKAz0ev6eeJO0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-SUM9WWxVgf0VNNBf9Zf7iga7Z4tkGvbKAz0ev6eeJO0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/xcpntce7vg-63fj8s7r0e.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/fzN5m4HpCinhQgyhv9a2GoLOChbhOzS2FxhpXWIfeE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/fzN5m4HpCinhQgyhv9a2GoLOChbhOzS2FxhpXWIfeE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/02vjf4n6ru-0j3bgjxly4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017905424"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "ETag", "Value": "\"SUM9WWxVgf0VNNBf9Zf7iga7Z4tkGvbKAz0ev6eeJO0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/02vjf4n6ru-0j3bgjxly4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"SUM9WWxVgf0VNNBf9Zf7iga7Z4tkGvbKAz0ev6eeJO0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SUM9WWxVgf0VNNBf9Zf7iga7Z4tkGvbKAz0ev6eeJO0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/gjniralk8i-notf2xhcfb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000033818059"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "ETag", "Value": "\"4mjnv8wEsIPqFZ44yOygGYlGftJdqqFxCTXXzEYGCTs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145401"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/gjniralk8i-notf2xhcfb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4mjnv8wEsIPqFZ44yOygGYlGftJdqqFxCTXXzEYGCTs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.gz"}, {"Name": "integrity", "Value": "sha256-4mjnv8wEsIPqFZ44yOygGYlGftJdqqFxCTXXzEYGCTs="}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/bootstrap/LICENSE.81b7ukuj9c", "AssetFile": "/App/SampleApp/wwwroot/lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "81b7ukuj9c"}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}, {"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/aezf3cup91-47otxtyo56.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214961307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "ETag", "Value": "\"LUJC/c/lAEUqUN5H7xI/zyKVpIQFL/ghfMpMmc8OywY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/aezf3cup91-47otxtyo56.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LUJC/c/lAEUqUN5H7xI/zyKVpIQFL/ghfMpMmc8OywY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz"}, {"Name": "integrity", "Value": "sha256-LUJC/c/lAEUqUN5H7xI/zyKVpIQFL/ghfMpMmc8OywY="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/aezf3cup91-47otxtyo56.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214961307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "ETag", "Value": "\"LUJC/c/lAEUqUN5H7xI/zyKVpIQFL/ghfMpMmc8OywY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/aezf3cup91-47otxtyo56.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LUJC/c/lAEUqUN5H7xI/zyKVpIQFL/ghfMpMmc8OywY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LUJC/c/lAEUqUN5H7xI/zyKVpIQFL/ghfMpMmc8OywY="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/sj0o4f8544-4v8eqarkd7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000452898551"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "ETag", "Value": "\"gCDjtbmTQ7oHsIcHiLvIK84vX7beVyMV9KBKNX7RvYA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/sj0o4f8544-4v8eqarkd7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gCDjtbmTQ7oHsIcHiLvIK84vX7beVyMV9KBKNX7RvYA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz"}, {"Name": "integrity", "Value": "sha256-gCDjtbmTQ7oHsIcHiLvIK84vX7beVyMV9KBKNX7RvYA="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/sj0o4f8544-4v8eqarkd7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000452898551"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "ETag", "Value": "\"gCDjtbmTQ7oHsIcHiLvIK84vX7beVyMV9KBKNX7RvYA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/sj0o4f8544-4v8eqarkd7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gCDjtbmTQ7oHsIcHiLvIK84vX7beVyMV9KBKNX7RvYA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gCDjtbmTQ7oHsIcHiLvIK84vX7beVyMV9KBKNX7RvYA="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/e6wheytmsa-356vix0kms.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001438848921"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "ETag", "Value": "\"HxJunWv7ekzhB184/5lStP91qJcW7XRDqOATbPYdAB0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/e6wheytmsa-356vix0kms.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"HxJunWv7ekzhB184/5lStP91qJcW7XRDqOATbPYdAB0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz"}, {"Name": "integrity", "Value": "sha256-HxJunWv7ekzhB184/5lStP91qJcW7XRDqOATbPYdAB0="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/e6wheytmsa-356vix0kms.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001438848921"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "ETag", "Value": "\"HxJunWv7ekzhB184/5lStP91qJcW7XRDqOATbPYdAB0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/e6wheytmsa-356vix0kms.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"HxJunWv7ekzhB184/5lStP91qJcW7XRDqOATbPYdAB0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HxJunWv7ekzhB184/5lStP91qJcW7XRDqOATbPYdAB0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ylttrv3j1o-83jwlth58m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071027772"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "ETag", "Value": "\"RA6FnFwvZwcy7PIS40oCJIxSKEHPVQl0ukyGVULfdIM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ylttrv3j1o-83jwlth58m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RA6FnFwvZwcy7PIS40oCJIxSKEHPVQl0ukyGVULfdIM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js.gz"}, {"Name": "integrity", "Value": "sha256-RA6FnFwvZwcy7PIS40oCJIxSKEHPVQl0ukyGVULfdIM="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ylttrv3j1o-83jwlth58m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071027772"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "ETag", "Value": "\"RA6FnFwvZwcy7PIS40oCJIxSKEHPVQl0ukyGVULfdIM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/ylttrv3j1o-83jwlth58m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RA6FnFwvZwcy7PIS40oCJIxSKEHPVQl0ukyGVULfdIM=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RA6FnFwvZwcy7PIS40oCJIxSKEHPVQl0ukyGVULfdIM="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/daradmyshj-mrlpezrjn3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000154249576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "ETag", "Value": "\"nAkd+bXDCLqrA9jmHlM5YCYXVOPFw+/pJ2IBWBBluZc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/daradmyshj-mrlpezrjn3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nAkd+bXDCLqrA9jmHlM5YCYXVOPFw+/pJ2IBWBBluZc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nAkd+bXDCLqrA9jmHlM5YCYXVOPFw+/pJ2IBWBBluZc="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/daradmyshj-mrlpezrjn3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000154249576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "ETag", "Value": "\"nAkd+bXDCLqrA9jmHlM5YCYXVOPFw+/pJ2IBWBBluZc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/daradmyshj-mrlpezrjn3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nAkd+bXDCLqrA9jmHlM5YCYXVOPFw+/pJ2IBWBBluZc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js.gz"}, {"Name": "integrity", "Value": "sha256-nAkd+bXDCLqrA9jmHlM5YCYXVOPFw+/pJ2IBWBBluZc="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/9lpbv05n85-lzl9nlhx6b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071078257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "ETag", "Value": "\"8wakphbzjcA3gIBAEAcZh4CWWnS9t3pv10WlHgDBKAQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52536"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/9lpbv05n85-lzl9nlhx6b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8wakphbzjcA3gIBAEAcZh4CWWnS9t3pv10WlHgDBKAQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8wakphbzjcA3gIBAEAcZh4CWWnS9t3pv10WlHgDBKAQ="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/9lpbv05n85-lzl9nlhx6b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071078257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "ETag", "Value": "\"8wakphbzjcA3gIBAEAcZh4CWWnS9t3pv10WlHgDBKAQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52536"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/9lpbv05n85-lzl9nlhx6b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8wakphbzjcA3gIBAEAcZh4CWWnS9t3pv10WlHgDBKAQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js.gz"}, {"Name": "integrity", "Value": "sha256-8wakphbzjcA3gIBAEAcZh4CWWnS9t3pv10WlHgDBKAQ="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/andlqkz2yp-ag7o75518u.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000123122384"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "ETag", "Value": "\"XlgSExzxNa3nkGlIRQzYeUGryaFcdCYYmDjJzMcVoSQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/andlqkz2yp-ag7o75518u.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XlgSExzxNa3nkGlIRQzYeUGryaFcdCYYmDjJzMcVoSQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js.gz"}, {"Name": "integrity", "Value": "sha256-XlgSExzxNa3nkGlIRQzYeUGryaFcdCYYmDjJzMcVoSQ="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/andlqkz2yp-ag7o75518u.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000123122384"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "ETag", "Value": "\"XlgSExzxNa3nkGlIRQzYeUGryaFcdCYYmDjJzMcVoSQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/andlqkz2yp-ag7o75518u.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XlgSExzxNa3nkGlIRQzYeUGryaFcdCYYmDjJzMcVoSQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XlgSExzxNa3nkGlIRQzYeUGryaFcdCYYmDjJzMcVoSQ="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/o5s4nqgah0-x0q3zqp4vz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001461988304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "ETag", "Value": "\"6HStD59bjkTPjQ3fGm7jnZcqoab/ANf+vA0DdOBKEcQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "W/\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.md.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/o5s4nqgah0-x0q3zqp4vz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"6HStD59bjkTPjQ3fGm7jnZcqoab/ANf+vA0DdOBKEcQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6HStD59bjkTPjQ3fGm7jnZcqoab/ANf+vA0DdOBKEcQ="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/o5s4nqgah0-x0q3zqp4vz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001461988304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "ETag", "Value": "\"6HStD59bjkTPjQ3fGm7jnZcqoab/ANf+vA0DdOBKEcQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "W/\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/o5s4nqgah0-x0q3zqp4vz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"6HStD59bjkTPjQ3fGm7jnZcqoab/ANf+vA0DdOBKEcQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md.gz"}, {"Name": "integrity", "Value": "sha256-6HStD59bjkTPjQ3fGm7jnZcqoab/ANf+vA0DdOBKEcQ="}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/5wyabwbwqr-0i3buxo5is.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011843851"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "ETag", "Value": "\"wKh8lMNTtLKwUKNMLP8uDqnSQs/rLxp7Le9dXpyys9A=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "285314"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/5wyabwbwqr-0i3buxo5is.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wKh8lMNTtLKwUKNMLP8uDqnSQs/rLxp7Le9dXpyys9A=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js.gz"}, {"Name": "integrity", "Value": "sha256-wKh8lMNTtLKwUKNMLP8uDqnSQs/rLxp7Le9dXpyys9A="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/5wyabwbwqr-0i3buxo5is.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011843851"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "ETag", "Value": "\"wKh8lMNTtLKwUKNMLP8uDqnSQs/rLxp7Le9dXpyys9A=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "285314"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/5wyabwbwqr-0i3buxo5is.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wKh8lMNTtLKwUKNMLP8uDqnSQs/rLxp7Le9dXpyys9A=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wKh8lMNTtLKwUKNMLP8uDqnSQs/rLxp7Le9dXpyys9A="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/zclkhg7zcn-o1o13a6vjx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032590275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "ETag", "Value": "\"OyU7TDIA0tTFKi3MRwhEaOd0kz4cUNzCtNouAea7Wq0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/zclkhg7zcn-o1o13a6vjx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OyU7TDIA0tTFKi3MRwhEaOd0kz4cUNzCtNouAea7Wq0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OyU7TDIA0tTFKi3MRwhEaOd0kz4cUNzCtNouAea7Wq0="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/sfdtm3yt31-ttgo8qnofa.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018363112"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "ETag", "Value": "\"Z9Xr9hTrQYEAWUwvg7CyNKwm+JkmM5dZcep0R6u6EHU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "134755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/sfdtm3yt31-ttgo8qnofa.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Z9Xr9hTrQYEAWUwvg7CyNKwm+JkmM5dZcep0R6u6EHU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z9Xr9hTrQYEAWUwvg7CyNKwm+JkmM5dZcep0R6u6EHU="}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/zclkhg7zcn-o1o13a6vjx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032590275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "ETag", "Value": "\"OyU7TDIA0tTFKi3MRwhEaOd0kz4cUNzCtNouAea7Wq0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/zclkhg7zcn-o1o13a6vjx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OyU7TDIA0tTFKi3MRwhEaOd0kz4cUNzCtNouAea7Wq0=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js.gz"}, {"Name": "integrity", "Value": "sha256-OyU7TDIA0tTFKi3MRwhEaOd0kz4cUNzCtNouAea7Wq0="}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/sfdtm3yt31-ttgo8qnofa.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018363112"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "ETag", "Value": "\"Z9Xr9hTrQYEAWUwvg7CyNKwm+JkmM5dZcep0R6u6EHU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "134755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/sfdtm3yt31-ttgo8qnofa.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Z9Xr9hTrQYEAWUwvg7CyNKwm+JkmM5dZcep0R6u6EHU=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map.gz"}, {"Name": "integrity", "Value": "sha256-Z9Xr9hTrQYEAWUwvg7CyNKwm+JkmM5dZcep0R6u6EHU="}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/49ru4jfgx1-2z0ns9nrw6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000014576834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "ETag", "Value": "\"Opr5hmrLHeEoBUrMugSdt/Vxh7nPIrdbPnxEe2XaKoA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js"}, {"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232015"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js"}, {"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/49ru4jfgx1-2z0ns9nrw6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Opr5hmrLHeEoBUrMugSdt/Vxh7nPIrdbPnxEe2XaKoA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js.gz"}, {"Name": "integrity", "Value": "sha256-Opr5hmrLHeEoBUrMugSdt/Vxh7nPIrdbPnxEe2XaKoA="}]}, {"Route": "lib/jquery/dist/jquery.slim.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/49ru4jfgx1-2z0ns9nrw6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000014576834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "ETag", "Value": "\"Opr5hmrLHeEoBUrMugSdt/Vxh7nPIrdbPnxEe2XaKoA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232015"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/49ru4jfgx1-2z0ns9nrw6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Opr5hmrLHeEoBUrMugSdt/Vxh7nPIrdbPnxEe2XaKoA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Opr5hmrLHeEoBUrMugSdt/Vxh7nPIrdbPnxEe2XaKoA="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/g9ngfeipjr-87fc7y1x7t.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000023188944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "ETag", "Value": "\"eZ5ql6+ipm5O69S+f/4DgMADzzYHAfKSgSmm36kNWC4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map"}, {"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107143"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map"}, {"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/g9ngfeipjr-87fc7y1x7t.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"eZ5ql6+ipm5O69S+f/4DgMADzzYHAfKSgSmm36kNWC4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map.gz"}, {"Name": "integrity", "Value": "sha256-eZ5ql6+ipm5O69S+f/4DgMADzzYHAfKSgSmm36kNWC4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/prk5ors1wn-muycvpuwrr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041049218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "ETag", "Value": "\"N3WCKGekGDic3HiyYi1ZwSMVPxgAzjd22gI/cZzwGGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/prk5ors1wn-muycvpuwrr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N3WCKGekGDic3HiyYi1ZwSMVPxgAzjd22gI/cZzwGGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N3WCKGekGDic3HiyYi1ZwSMVPxgAzjd22gI/cZzwGGs="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/g9ngfeipjr-87fc7y1x7t.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000023188944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "ETag", "Value": "\"eZ5ql6+ipm5O69S+f/4DgMADzzYHAfKSgSmm36kNWC4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107143"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/g9ngfeipjr-87fc7y1x7t.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"eZ5ql6+ipm5O69S+f/4DgMADzzYHAfKSgSmm36kNWC4=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eZ5ql6+ipm5O69S+f/4DgMADzzYHAfKSgSmm36kNWC4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/prk5ors1wn-muycvpuwrr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041049218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "ETag", "Value": "\"N3WCKGekGDic3HiyYi1ZwSMVPxgAzjd22gI/cZzwGGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js"}, {"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/dist/jquery.slim.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js"}, {"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/prk5ors1wn-muycvpuwrr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N3WCKGekGDic3HiyYi1ZwSMVPxgAzjd22gI/cZzwGGs=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js.gz"}, {"Name": "integrity", "Value": "sha256-N3WCKGekGDic3HiyYi1ZwSMVPxgAzjd22gI/cZzwGGs="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/4lkxi7qll4-mlv21k5csn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001464128843"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "ETag", "Value": "\"Pu7EEldYFfJduO8Z+fI/nS9U6SNQun+UzT1IrRHJ4oA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/4lkxi7qll4-mlv21k5csn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Pu7EEldYFfJduO8Z+fI/nS9U6SNQun+UzT1IrRHJ4oA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt.gz"}, {"Name": "integrity", "Value": "sha256-Pu7EEldYFfJduO8Z+fI/nS9U6SNQun+UzT1IrRHJ4oA="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/4lkxi7qll4-mlv21k5csn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001464128843"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "ETag", "Value": "\"Pu7EEldYFfJduO8Z+fI/nS9U6SNQun+UzT1IrRHJ4oA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "/App/SampleApp/wwwroot/lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:05:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.txt.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/4lkxi7qll4-mlv21k5csn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Pu7EEldYFfJduO8Z+fI/nS9U6SNQun+UzT1IrRHJ4oA=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Pu7EEldYFfJduO8Z+fI/nS9U6SNQun+UzT1IrRHJ4oA="}]}, {"Route": "SampleApp.1b6d9wnjpo.bundle.scp.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p5g9so0ja5-1b6d9wnjpo.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001865671642"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "535"}, {"Name": "ETag", "Value": "\"0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b6d9wnjpo"}, {"Name": "label", "Value": "SampleApp.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE="}]}, {"Route": "SampleApp.1b6d9wnjpo.bundle.scp.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/scopedcss/projectbundle/SampleApp.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1078"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b6d9wnjpo"}, {"Name": "label", "Value": "SampleApp.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE="}]}, {"Route": "SampleApp.1b6d9wnjpo.bundle.scp.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p5g9so0ja5-1b6d9wnjpo.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "535"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b6d9wnjpo"}, {"Name": "label", "Value": "SampleApp.bundle.scp.css.gz"}, {"Name": "integrity", "Value": "sha256-0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY="}]}, {"Route": "SampleApp.1b6d9wnjpo.styles.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0ic3yl05sj-1b6d9wnjpo.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001865671642"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "535"}, {"Name": "ETag", "Value": "\"0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b6d9wnjpo"}, {"Name": "label", "Value": "SampleApp.styles.css"}, {"Name": "integrity", "Value": "sha256-LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE="}]}, {"Route": "SampleApp.1b6d9wnjpo.styles.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/scopedcss/bundle/SampleApp.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1078"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b6d9wnjpo"}, {"Name": "label", "Value": "SampleApp.styles.css"}, {"Name": "integrity", "Value": "sha256-LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE="}]}, {"Route": "SampleApp.1b6d9wnjpo.styles.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0ic3yl05sj-1b6d9wnjpo.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "535"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b6d9wnjpo"}, {"Name": "label", "Value": "SampleApp.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY="}]}, {"Route": "SampleApp.bundle.scp.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p5g9so0ja5-1b6d9wnjpo.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001865671642"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "535"}, {"Name": "ETag", "Value": "\"0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE="}]}, {"Route": "SampleApp.bundle.scp.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/scopedcss/projectbundle/SampleApp.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1078"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE="}]}, {"Route": "SampleApp.bundle.scp.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/p5g9so0ja5-1b6d9wnjpo.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "535"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY="}]}, {"Route": "SampleApp.styles.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0ic3yl05sj-1b6d9wnjpo.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001865671642"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "535"}, {"Name": "ETag", "Value": "\"0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE="}]}, {"Route": "SampleApp.styles.css", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/scopedcss/bundle/SampleApp.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1078"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LboGJHjvRVYqOhAFCdSTXOpAKhmgrTrcgmulKkFZ2BE="}]}, {"Route": "SampleApp.styles.css.gz", "AssetFile": "/App/SampleApp/obj/Debug/net9.0/compressed/0ic3yl05sj-1b6d9wnjpo.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "535"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY=\""}, {"Name": "Last-Modified", "Value": "Sun, 07 Sep 2025 14:06:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0RO122nCzHBp3QsepR7RaLu/NR3m5M+3Dwa4oyg9lPY="}]}]}