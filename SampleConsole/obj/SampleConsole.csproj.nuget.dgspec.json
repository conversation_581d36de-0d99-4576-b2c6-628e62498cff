{"format": 1, "restore": {"/App/SampleConsole/SampleConsole.csproj": {}}, "projects": {"/App/SampleConsole/SampleConsole.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/App/SampleConsole/SampleConsole.csproj", "projectName": "SampleConsole", "projectPath": "/App/SampleConsole/SampleConsole.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/App/SampleConsole/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}