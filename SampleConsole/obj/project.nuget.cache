{"version": 2, "dgSpecHash": "r31/3kQ6DTU=", "success": true, "projectFilePath": "/App/SampleConsole/SampleConsole.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/microsoft.extensions.configuration/9.0.8/microsoft.extensions.configuration.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.8/microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/9.0.8/microsoft.extensions.configuration.binder.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.8/microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.8/microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.8/microsoft.extensions.logging.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.8/microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.configuration/9.0.8/microsoft.extensions.logging.configuration.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.console/9.0.8/microsoft.extensions.logging.console.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.options/9.0.8/microsoft.extensions.options.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/9.0.8/microsoft.extensions.options.configurationextensions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.8/microsoft.extensions.primitives.9.0.8.nupkg.sha512"], "logs": []}